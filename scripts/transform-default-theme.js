const fs = require("fs");
const path = require("path");

const darkThemePath = path.join(
  __dirname,
  "../webview-ui/theme/input-default-dark.json",
);
const lightThemePath = path.join(
  __dirname,
  "../webview-ui/theme/input-default-light.json",
);
const cssOutputPath = path.join(
  __dirname,
  "../webview-ui/src/style/default-theme.css",
);

const outputDarkThemePath = path.join(
  __dirname,
  "../webview-ui/theme/output-default-dark.json",
);
const outputLightThemePath = path.join(
  __dirname,
  "../webview-ui/theme/output-default-light.json",
);
function readThemeFile(filePath) {
  const fileContent = fs.readFileSync(filePath, "utf-8");
  return eval("(" + fileContent + ")");
}

function generateCSSVariables(theme) {
  const colors = theme.colors || {};
  const res = {};
  for (const [key, value] of Object.entries(colors)) {
    let newKey = key.replace(/\./g, "-");
    newKey = `--vscode-${newKey}`;
    res[newKey] = value;
  }
  return res;
}

function writeCSSVariablesToTheme(cssVariables, themeClass) {
  const cssContent = `.${themeClass} {\n  ${cssVariables}\n}\n`;
}

const darkTheme = readThemeFile(darkThemePath);
const lightTheme = readThemeFile(lightThemePath);
darkTheme.name = "kwaipilot-default-theme-dark";
lightTheme.name = "kwaipilot-default-theme-light";

/** 设置默认主题名称写入新的文件 */
fs.writeFileSync(
  outputDarkThemePath,
  JSON.stringify(darkTheme, null, 2),
  "utf-8",
);
fs.writeFileSync(
  outputLightThemePath,
  JSON.stringify(lightTheme, null, 2),
  "utf-8",
);

const darkCSSVariables = generateCSSVariables(darkTheme);
const lightCSSVariables = generateCSSVariables(lightTheme);

function getCssContent(dark = {}, light = {}) {
  return `.default-theme.light {
  ${Object.entries(light)
    .map(([key, value]) => `${key}: ${value};`)
    .join("\n")}
}

.default-theme.dark {
   ${Object.entries(dark)
      .map(([key, value]) => `${key}: ${value};`)
      .join("\n")}
}
`;
}

fs.writeFileSync(
  cssOutputPath,
  getCssContent(darkCSSVariables, lightCSSVariables),
  "utf-8",
);

console.log("CSS variables generated and written to default-theme.css");
