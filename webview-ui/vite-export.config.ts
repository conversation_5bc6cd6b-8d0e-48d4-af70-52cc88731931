// NOTE: 用于ide 产物编译，修改时需要注意是否同步 ./vite.config.ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "tailwindcss";
import autoprefixer from "autoprefixer";
import tsconfigPaths from "vite-tsconfig-paths";
import { resolve } from "path";
import svgr from "vite-plugin-svgr";
import fs from "fs";
import MagicString from "magic-string";

// 定义多个入口点
const entries = {
  "mount-export": resolve(__dirname, "src/mount-export.tsx"),
  // 你可以添加更多入口点
  // 'other-component': resolve(__dirname, 'src/components/OtherComponent.tsx'),
};

const replaceInnerhtml = {
  name: "replace-innerhtml",
  renderChunk(code, chunk) {
    const magicString = new MagicString(code);
    let hasReplaced = false;

    let searchIndex = 0;
    let foundIndex;

    while ((foundIndex = code.indexOf(".innerHTML", searchIndex)) !== -1) {
      magicString.overwrite(
        foundIndex,
        foundIndex + ".innerHTML".length,
        ".TrustedHTML",
      );
      searchIndex = foundIndex + ".innerHTML".length;
      hasReplaced = true;
    }
    if (hasReplaced) {
      magicString.prepend("// @ts-nocheck\n");

      return {
        code: magicString.toString(),
        map: magicString.generateMap({
          hires: true,
        }),
      };
    }

    return null;
  },
};

// https://vitejs.dev/config/
export default defineConfig({
  base: "./",
  resolve: {
    alias: {
      "@/bridge": resolve(__dirname, "./src/bridge-export"),
      "@": resolve(__dirname, "src"),
      "@shared": resolve(__dirname, "../src/shared"),
      "@ks-kwaipilot/artifact-message-parser": resolve(
        __dirname,
        "../packages/artifact-message-parser",
      ),
    },
  },
  plugins: [tsconfigPaths(), react(), svgr()],
  // 添加define配置，替换环境变量为具体值
  define: {
    "process.env.NODE_ENV": JSON.stringify(
      process.env.NODE_ENV || "production",
    ),
    "process.env": JSON.stringify({}),
    // 直接替换全局对象引用，避免运行时检测
    "global": "window",
    "globalThis": "window",
    // 如果有其他特定环境变量需要保留，可以单独列出
    // 'process.env.API_URL': JSON.stringify(process.env.API_URL),
  },
  server: {
    cors: {
      origin: ["vscode-webview://*"],
      methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
      allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
      credentials: true,
    },
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers":
        "Content-Type, Authorization, X-Requested-With",
    },
    hmr: {
      protocol: "ws",
      host: "localhost",
      port: 5173,
      clientPort: 5173,
      overlay: true,
      timeout: 30000,
    },
    watch: {
      usePolling: true,
      interval: 100,
    },
    strictPort: true,
    port: 5173,
  },
  build: {
    outDir: "dist-export",
    minify: "esbuild", // 使用esbuild进行最小化
    target: "esnext", // 针对现代浏览器，减少兼容性代码
    sourcemap: true,
    lib: {
      // 配置入口点
      entry: entries,
      formats: ["es"],
    },
    rollupOptions: {
      // 声明哪些模块是外部依赖，不会被打包进库
      external: [
        // 'react',
        // 'react-dom',
        // '@chakra-ui/react',
        // '@chakra-ui/icons',
        // '@emotion/react',
        // '@emotion/styled',
        // 'framer-motion'
      ],
      output: {
        // 为外部化的依赖提供全局变量名
        globals: {
          // react: 'React',
          // 'react-dom': 'ReactDOM',
          // '@chakra-ui/react': 'ChakraUI',
          // '@chakra-ui/icons': 'ChakraIcons',
          // '@emotion/react': 'emotionReact',
          // '@emotion/styled': 'emotionStyled',
          // 'framer-motion': 'framerMotion'
        },
        entryFileNames: `[name].js`,
        inlineDynamicImports: true, // 内联所有动态导入，将所有代码打包到一个文件
        format: "esm", // 确保使用ESM格式，减少辅助代码
        generatedCode: {
          symbols: true,
          objectShorthand: true, // 使用对象简写语法
          arrowFunctions: true, // 使用箭头函数
          constBindings: true, // 使用const绑定
        },
        preserveModules: false,
        // 添加输出配置，确保不生成特定的运行时辅助代码
        interop: "auto", // 控制生成的interop辅助代码
        externalLiveBindings: false, // 不使用外部活动绑定
        freeze: false, // 不冻结命名空间对象
      },
      onwarn(warning: any, warn: any) {
        if (warning.code === "MODULE_LEVEL_DIRECTIVE") {
          return;
        }
        if (
          warning.code === "CIRCULAR_DEPENDENCY"
          || warning.code === "UNRESOLVED_IMPORT"
        ) {
          return;
        }
        warn(warning);
      },
      plugins: [
        {
          name: "generate-version",
          writeBundle() {
            const version = {
              version: process.env.npm_package_version || "1.0.0",
            };
            fs.writeFileSync(
              resolve(__dirname, "dist-export/version.json"),
              JSON.stringify(version, null, 2),
            );
          },
        },
        replaceInnerhtml,
      ],
    },
    commonjsOptions: {
      // 控制CommonJS模块的转换选项
      transformMixedEsModules: true,
      strictRequires: true,
    },
  },
  css: {
    postcss: {
      plugins: [
        tailwindcss,
        autoprefixer,
        // 添加自定义 PostCSS 插件来包装 CSS
        {
          postcssPlugin: "scope-css",
          Once(root) {
            // 添加作用域选择器
            root.walkRules((rule) => {
              // 跳过 @keyframes 规则
              if (
                rule.parent?.type === "atrule"
                && rule.parent?.name === "keyframes"
              ) {
                return;
              }

              // 跳过 module
              if (
                rule.source?.input.file?.endsWith(".module.less")
                || rule.source?.input.file?.endsWith(".module.sass")
                || rule.source?.input.file?.endsWith(".module.scss")
              )
                return;

              // 处理全局选择器和伪元素选择器
              if (!rule.selector.includes(".ai-assistant-container")) {
                // 处理以 *, ::before, ::after 开头的选择器
                if (
                  rule.selector.includes("*")
                  || rule.selector.includes("::")
                ) {
                  // 将选择器分割成多个部分
                  const selectors = rule.selector
                    .split(",")
                    .map(s => s.trim());
                  // 为每个选择器添加容器类名
                  const newSelectors = selectors.map(
                    selector => `.ai-assistant-container ${selector}`,
                  );
                  rule.selector = newSelectors.join(", ");
                }
                else {
                  rule.selector = `.ai-assistant-container ${rule.selector}`;
                }
              }
            });
          },
        },
      ],
    },
    modules: {
      localsConvention: "camelCase",
    },
  },
  // 优化esbuild配置
  esbuild: {
    legalComments: "none", // 删除所有注释
    target: "es2020", // 明确设置目标
    drop: ["console", "debugger"], // 删除console和debugger语句
  },
});
