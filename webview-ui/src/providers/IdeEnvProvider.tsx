import React, { createContext } from "react";

interface IdeEnvContextType {
  isKwaiPilotIDE?: boolean;
}

export const IdeEnvContext = createContext<IdeEnvContextType | undefined>(
  undefined,
);

interface IdeEnvProviderProps {
  children: React.ReactNode;
  value?: IdeEnvContextType;
}

export const IdeEnvProvider: React.FC<IdeEnvProviderProps> = ({
  children,
  value,
}) => {
  return (
    <IdeEnvContext.Provider value={value}>{children}</IdeEnvContext.Provider>
  );
};
