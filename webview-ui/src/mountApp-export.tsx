// NOTE: ide 组件，不影响插件逻辑
import React from "react";
import * as ReactDOM from "react-dom/client";
import { ChakraProvider } from "@chakra-ui/react";
import App from "@/App";
import theme from "@/utils/theme";
import CodeImage from "@/components/Prediction/codeImage";
import { IdeEnvProvider } from "@/providers/IdeEnvProvider";
import { getCurrentEnvIsInIDE } from "./utils/ide";
/**
 * mountApp函数的选项接口
 */
interface MountAppOptions {
  customTheme?: any;
}

/**
 * 挂载应用到指定DOM元素
 *
 * @param rootElement - 要挂载应用的DOM元素
 * @param options - 配置选项，可自定义主题等
 * @returns 包含重新渲染和销毁方法的对象
 */
const mountApp = async (rootElement: HTMLElement, options: MountAppOptions) => {
  if (typeof document === "undefined") {
    console.error("mount.tsx error: document was undefined");
    return {
      rerender: () => {},
      dispose: () => {},
    };
  }

  const root = ReactDOM.createRoot(rootElement);

  const rerender = () => {
    root.render(
      <ChakraProvider theme={options?.customTheme || theme}>
        <IdeEnvProvider
          value={{ isKwaiPilotIDE: getCurrentEnvIsInIDE() }}
        >
          <CodeImage />
          <App />
        </IdeEnvProvider>
      </ChakraProvider>,
    );
  };

  const dispose = () => {
    root.unmount();
  };

  rerender();

  return {
    rerender,
    dispose,
  };
};

export default mountApp;
