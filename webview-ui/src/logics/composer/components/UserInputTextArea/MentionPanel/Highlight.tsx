import { useColorMode } from "@chakra-ui/react";
import { useMemo } from "react";

interface IProps {
  text: string;
  highlight: string;
}

export const HighlightText: React.FC<IProps> = (props: IProps) => {
  const { text, highlight } = props;
  // if (!text) {
  //   debugger;
  // }
  const parts = text.split(new RegExp(`(${highlight})`, "gi"));

  const { colorMode: theme } = useColorMode();
  const isDark = theme === "dark";

  const style = useMemo(() => {
    const style: React.CSSProperties = {
      fontFeatureSettings: `'clig' off, 'liga' off`,
      fontWeight: "500",
    };

    if (isDark) {
      style.color = "#3077E2";
    }
    else {
      style.color = "#326BFB";
    }
    return style;
  }, [isDark]);

  return (
    <div className="truncate">
      {/* 这里无论如何都会触发tooltip 好奇怪 */}
      {/* <AutoTooltip label={text} placement="top">

      </AutoTooltip> */}

      {parts.map((part, index) =>
        // 不区分大小写地检查是否为高亮部分
        part.toLowerCase() === highlight.toLowerCase()
          ? (
              <span key={index} style={style}>
                {part}
              </span>
            )
          : (
              part
            ),
      )}
    </div>
  );
};
