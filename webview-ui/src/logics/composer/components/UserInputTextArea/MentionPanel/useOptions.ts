import { useMemo } from "react";
import { NewPanelFirstStageMenuType } from "./MentionPanel";
import { RichEditorBoxPanelData, RichEditorMenuType } from "shared/lib/richEditor/const";
import { SharpCommand } from "@shared/types";
import { useRichEditPanelMenuStore } from "@/store/richEditorPanelMenu";
import { throwNeverError } from "@/utils/throwUnknownError";
import { getSortFileOrDir } from "../../../../../components/TextArea/utils";
import { MenuOption } from "@lexical/react/LexicalTypeaheadMenuPlugin";
import { MentionNodeV2Structure_File, MentionNodeV2Structure_Rule, MentionNodeV2Structure_Tree } from "shared/lib/MentionNodeV2/nodes";
import { CustomPromptData } from "shared/lib/CustomVariable";
import { useBridgeObservableAPI } from "@/bridge/useBridgeObservableAPI";
import { basename } from "path-browserify";

export enum TypeaheadMenuOptionType {
  /** 带子菜单的 */
  heading = "heading",
  /** 文件 */
  file = "file",
  /** 文件夹 */
  folder = "folder",
  /** 项目规则 */
  rule = "rule",
  /** 添加新的规则 */
  addRule = "addRule",
  /** 初始状态一级面板 */
  none = "none",
  /** 头部 */
  header = "header",
  /** 自定义指令 */
  customPrompt = "customPrompt",
}

export class MentionTypeaheadOption<T extends TypeaheadMenuOptionType = TypeaheadMenuOptionType > extends MenuOption {
  type: T;
  name: string;
  secondaryText: string;

  structure: T extends TypeaheadMenuOptionType.file
    ? MentionNodeV2Structure_File
    : T extends TypeaheadMenuOptionType.folder
      ? MentionNodeV2Structure_Tree
      : T extends TypeaheadMenuOptionType.customPrompt
        ? CustomPromptData
        : T extends TypeaheadMenuOptionType.rule
          ? MentionNodeV2Structure_Rule
          : T extends TypeaheadMenuOptionType.addRule
            ? undefined
            : null;

  constructor(type: T, name: string, secondaryText: string, structure: MentionTypeaheadOption<T>["structure"]) {
    super(name);
    this.type = type;
    this.name = name;
    this.secondaryText = secondaryText;
    this.structure = structure;
  }
}

const getDefaultOptions = () => {
  const options: MentionTypeaheadOption<any>[] = [
    new MentionTypeaheadOption(TypeaheadMenuOptionType.heading, TypeaheadMenuOptionType.file, "文件", null),
    new MentionTypeaheadOption(TypeaheadMenuOptionType.heading, TypeaheadMenuOptionType.folder, "目录", null),
    new MentionTypeaheadOption(TypeaheadMenuOptionType.heading, TypeaheadMenuOptionType.rule, "规则", null),
  ];
  return options;
};

function BoxPanelData2MentionTypeaheadOption(data: RichEditorBoxPanelData): MentionTypeaheadOption {
  return data.key === SharpCommand.FOLDER
    ? new MentionTypeaheadOption<TypeaheadMenuOptionType.folder>(
      TypeaheadMenuOptionType.folder,
      data.title,
      data.description,
      {
        type: "tree",
        uri: data.uri,
        relativePath: data.data,
      },
    )
    : data.key === SharpCommand.RULES
      ? new MentionTypeaheadOption<TypeaheadMenuOptionType.rule>(
        TypeaheadMenuOptionType.rule,
        data.title,
        data.description,
        {
          type: "rule",
          uri: data.uri,
          relativePath: data.data,
        },
      )
      : new MentionTypeaheadOption<TypeaheadMenuOptionType.file>(
        TypeaheadMenuOptionType.file,
        data.title,
        data.description,
        {
          type: "file",
          uri: data.uri,
          relativePath: data.data,
        },
      );
}

export function useOptions({ currentMenu,
  queryString,
  menuType,
}: {
  currentMenu: TypeaheadMenuOptionType;
  queryString: string;
  menuType: NewPanelFirstStageMenuType;
}) {
  const codeSearchDefaultDir = useRichEditPanelMenuStore(state => state.codeSearchDefaultDir);
  const codeSearchDefaultFiles = useRichEditPanelMenuStore(state => state.codeSearchDefaultFiles);
  const codeSearchWorkspaceDir = useRichEditPanelMenuStore(state => state.codeSearchWorkspaceDir);
  const codeSearchWorkspaceFiles = useRichEditPanelMenuStore(state => state.codeSearchWorkspaceFiles);
  const ruleFiles = useRichEditPanelMenuStore(state => state.ruleFiles);
  const slashCommand = useRichEditPanelMenuStore(state => state.slashCommand);
  const disabledMenu = useRichEditPanelMenuStore(state => state.disabledMenu);

  const currentFileAndSelection = useBridgeObservableAPI("currentFileAndSelection");

  const options = useMemo<MentionTypeaheadOption[]>(() => {
    if ((queryString === null || queryString === "") && currentMenu === TypeaheadMenuOptionType.none) {
      return getDefaultOptions();
    }
    else if (currentMenu === TypeaheadMenuOptionType.none) {
      if (menuType === RichEditorMenuType.SHARP_COMMAND) {
        // 处理带 query 的情况
        const list = disabledMenu[SharpCommand.FOLDER]
          .status
          ? [...codeSearchWorkspaceFiles,
              ...ruleFiles,
            ]
          : [
              ...codeSearchWorkspaceDir,
              ...codeSearchWorkspaceFiles,
              ...ruleFiles,
            ];
        return getSortFileOrDir(list, queryString)
          .map(BoxPanelData2MentionTypeaheadOption);
      }
      if (menuType === RichEditorMenuType.SLASH_COMMAND) {
        const res: RichEditorBoxPanelData[] = [];
        slashCommand.forEach((item) => {
          if (
            item.search?.some(searchItem =>
              searchItem.toLowerCase().startsWith(queryString.toLowerCase()),
            )
          ) {
            res.push(item);
          }
        });

        return res.map(BoxPanelData2MentionTypeaheadOption);
      }

      throwNeverError(menuType);
    }
    else {
      switch (currentMenu) {
        case TypeaheadMenuOptionType.file: {
          if (!queryString) {
            let fileList = codeSearchDefaultFiles.map(BoxPanelData2MentionTypeaheadOption);
            if (currentFileAndSelection) {
              fileList = [
                new MentionTypeaheadOption<TypeaheadMenuOptionType.file>(
                  TypeaheadMenuOptionType.file,
                  basename(currentFileAndSelection.relativePath),
                  currentFileAndSelection.relativePath,
                  {
                    type: "file",
                    uri: currentFileAndSelection.uri,
                    relativePath: currentFileAndSelection.relativePath,
                  },
                ),
                ...fileList.filter(v => (v as MentionTypeaheadOption<TypeaheadMenuOptionType.file>).structure.uri !== currentFileAndSelection.uri),
              ];
            }
            return [
              new MentionTypeaheadOption(TypeaheadMenuOptionType.header, "文件", "", null),
              ...fileList,
            ];
          }
          else {
            const rawList = codeSearchDefaultFiles || [];
            return [
              new MentionTypeaheadOption(TypeaheadMenuOptionType.header, "文件", "", null),
              ...getSortFileOrDir(
                rawList,
                queryString,
              ).map(BoxPanelData2MentionTypeaheadOption),
            ];
          }
        }
        case TypeaheadMenuOptionType.folder: {
          if (!queryString) {
            return [
              new MentionTypeaheadOption(TypeaheadMenuOptionType.header, "目录", "", null),
              ...codeSearchDefaultDir.map(BoxPanelData2MentionTypeaheadOption),
            ];
          }
          else {
            const rawList = codeSearchDefaultDir || [];
            return [
              new MentionTypeaheadOption(TypeaheadMenuOptionType.header, "目录", "", null),
              ...getSortFileOrDir(
                rawList,
                queryString,
              ).map(BoxPanelData2MentionTypeaheadOption),
            ];
          }
        }
        case TypeaheadMenuOptionType.rule: {
          if (!queryString) {
            return [
              new MentionTypeaheadOption(TypeaheadMenuOptionType.header, "规则", "", null),
              ...ruleFiles.map(BoxPanelData2MentionTypeaheadOption),
            ];
          }
          else {
            const rawList = ruleFiles || [];
            return [
              new MentionTypeaheadOption(TypeaheadMenuOptionType.header, "规则", "", null),
              ...getSortFileOrDir(
                rawList,
                queryString,
              ).map(BoxPanelData2MentionTypeaheadOption),
            ];
          }
        }
      }
    }
    return [];
  }, [queryString, currentMenu, menuType, disabledMenu, codeSearchWorkspaceFiles, ruleFiles, codeSearchWorkspaceDir, slashCommand, codeSearchDefaultFiles, currentFileAndSelection, codeSearchDefaultDir]);
  return {
    options,
  };
}
