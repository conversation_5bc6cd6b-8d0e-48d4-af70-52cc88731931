import { useContext, useEffect, useMemo, useRef } from "react";
import { EmptyText } from "./EmptyText";
import {
  MenuItem,
  MenuItem_File,
  MenuItem_Header,
  MenuItem_Heading,
} from "./MenuItem";
import { MentionPanelContext, useMentionPanelContext } from "./MentionPanel";
import { RichEditorMenuType } from "../../../../../components/TextArea/const";
import { useRichEditPanelMenuStore } from "@/store/richEditorPanelMenu";
import { getSortFileOrDir } from "../../../../../components/TextArea/utils";
import { Popover, Portal } from "@/components/Union/chakra-ui";
import {
  PopoverTrigger,
  PopoverContent,
  Link,
  Spinner,
  Box,
} from "@chakra-ui/react";

import { ExternalLinkIcon } from "@chakra-ui/icons";
import { useDesignToken } from "@/hooks/useDesignToken";
import { useScrolling } from "react-use";
import {
  CustomScrollBar,
  useCustomScrollBar,
} from "@/components/CustomScrollbar";
import { Env } from "@/http/env";
import { weblog } from "@/utils/weblogger";
import { MentionTypeaheadOption, TypeaheadMenuOptionType } from "./useOptions";
import { useTypeaheadMenuContext } from "@/components/TextArea/lexical/MentionsV2Plugin";
import { getRootContainer } from "@/utils/dom";

const PROMPT_ARTIST_PLATFORM_URL = Env.IS_PROD
  ? "https://prompt-artist.corp.kuaishou.com"
  : "https://prompt-artist.staging.kuaishou.com";

export const CUSTOM_PROMPT_POPOVER_CONTENT_CLASS_NAME
  = "custom-prompt-popover-content";

function CustomPromptItem({
  item,
  index,
  isRecent,
}: {
  item: MentionTypeaheadOption<TypeaheadMenuOptionType.customPrompt>;
  index: number;
  isRecent: boolean;
}) {
  const { submittingIndex, selectIndex } = useMentionPanelContext();

  const containerRef = useRef<HTMLElement>(getRootContainer());

  const { container: scrollContainer } = useCustomScrollBar();
  const isScrolling = useScrolling(scrollContainer);

  const menuItem = (
    <MenuItem index={index} data={item} disabled={false} disabledMsg="">
      <span className=" flex-none text-text-common-primary font-medium">
        {item.structure.name}
      </span>
      {isRecent && index === selectIndex && (
        <span className=" h-[18px] bg-tag-bg-kwaipilot rounded ml-[6px]">
          <span className=" text-text-common-secondary text-[12px] scale-[0.83] whitespace-nowrap inline-block">
            最近
          </span>
        </span>
      )}
      <span className="pl-4 whitespace-nowrap text-ellipsis overflow-hidden text-text-common-tertiary text-[12px] ml-auto">
        {item.structure.content}
      </span>
      {submittingIndex === index && <Spinner ml="4px" size="xs" />}
    </MenuItem>
  );

  if (isScrolling) {
    return menuItem;
  }
  return (
    <Popover
      trigger="hover"
      placement="top-end"
      boundary="clippingParents"
      key={item.structure.id}
    >
      <PopoverTrigger>
        <div>{menuItem}</div>
      </PopoverTrigger>
      <Portal containerRef={containerRef}>
        <PopoverContent
          w="270px"
          maxW="70vw"
          maxH={200}
          tabIndex={-1}
          className={CUSTOM_PROMPT_POPOVER_CONTENT_CLASS_NAME}
        >
          <div className=" p-2">
            <div className=" text-text-common-primary text-[13px] whitespace-nowrap leading-[20px] font-medium">
              {item.structure.name}
            </div>
          </div>
          <div className=" px-2 py-1 flex-auto overflow-y-auto">
            <div className=" text-text-common-secondary text-[12px] leading-[18px]">
              {item.structure.content}
            </div>
          </div>
          <div className=" px-2 py-[6px] flex justify-between items-center  border-0 border-t-[1px] border-t-border-horizontal">
            <div className=" text-text-common-secondary text-[12px] leading-[18px]">
              @
              {item.structure.owner_id}
            </div>
            <div className=" text-text-brand-default text-[12px] leading-[18px] ">
              <Link
                isExternal
                href={`${PROMPT_ARTIST_PLATFORM_URL}/playground?id=${item.structure.id}&bizCode=kwaipilot`}
                color="current"
              >
                提示词管理平台
                <ExternalLinkIcon color="current" mx="2px" />
              </Link>
            </div>
          </div>
        </PopoverContent>
      </Portal>
    </Popover>
  );
}

function MenuListCustomPrompt() {
  const { query, recentUsedPrompts } = useMentionPanelContext();
  const panelMenuStore = useRichEditPanelMenuStore();

  const { tokens } = useDesignToken();

  const recentUsedPromptIds = useMemo(() => {
    return recentUsedPrompts.map(item => item.id);
  }, [recentUsedPrompts]);

  const customPromptsHitResult = useMemo<
    MentionTypeaheadOption<TypeaheadMenuOptionType.customPrompt>[]
  >(() => {
    return getSortFileOrDir(panelMenuStore.customPrompts, query).map(
      prompt =>
        new MentionTypeaheadOption(
          TypeaheadMenuOptionType.customPrompt,
          prompt.data,
          prompt.description,
          prompt.raw,
        ),
    );
  }, [panelMenuStore.customPrompts, query]);

  const recentUserdPrompts = useMemo(() => {
    return customPromptsHitResult.filter(item =>
      recentUsedPromptIds.includes(item.structure.id),
    );
  }, [customPromptsHitResult, recentUsedPromptIds]);
  const restPrompts = useMemo(() => {
    return customPromptsHitResult.filter(
      item => !recentUsedPromptIds.includes(item.structure.id),
    );
  }, [customPromptsHitResult, recentUsedPromptIds]);

  // 添加曝光埋点
  useEffect(() => {
    weblog?.sendImmediately("SHOW", {
      action: "VS_CUSTOM_PROMPT_PANEL",
    });
  }, []);

  const noPropmpts = !panelMenuStore.customPrompts.length;

  const noHitQueryPrompts = query && !customPromptsHitResult.length;

  return (
    <>
      {noPropmpts || noHitQueryPrompts
        ? (
            <div className=" flex flex-col items-center py-[31px]">
              <img
                src="https://ali.a.yximgs.com/kos/nlav12119/lSiPplHt_2025-02-14-11-34-51.png"
                width={68}
              >
              </img>
              <div className=" mt-2">
                暂无自定义指令，前往
                <Link
                  href={`${PROMPT_ARTIST_PLATFORM_URL}/index`}
                  textDecoration="none"
                  color={tokens.colorTextBrandDefault}
                  isExternal
                >
                  提示词管理平台
                </Link>
                新建
              </div>
            </div>
          )
        : (
            <>
              {recentUserdPrompts.map((item, index) => {
                return (
                  <CustomPromptItem
                    key={item.structure.id}
                    item={item}
                    index={index}
                    isRecent={true}
                  />
                );
              })}
              {recentUserdPrompts.length > 0 && restPrompts.length > 0 && (
                <div className="h-[1px] bg-border-horizontal w-full"></div>
              )}
              {restPrompts.map((item, index) => {
                return (
                  <CustomPromptItem
                    key={item.structure.id}
                    item={item}
                    index={index + recentUserdPrompts.length}
                    isRecent={false}
                  />
                );
              })}
            </>
          )}
    </>
  );
}

function MenuListCommon() {
  const { options: _options } = useMentionPanelContext();

  const indexedOptions = useMemo(() => {
    return _options.map((option, index) => ({
      data: option,
      index,
    }));
  }, [_options]);

  const headerItem = useMemo(
    () =>
      indexedOptions.find(
        v => v.data.type === TypeaheadMenuOptionType.header,
      ),
    [indexedOptions],
  );
  const options = useMemo(
    () =>
      indexedOptions.filter(
        v => v.data.type !== TypeaheadMenuOptionType.header,
      ),
    [indexedOptions],
  );

  return (
    <>
      {headerItem && (
        <MenuItem_Header
          item={
            headerItem.data as MentionTypeaheadOption<TypeaheadMenuOptionType.header>
          }
          index={headerItem.index}
        />
      )}

      <CustomScrollBar suppressScrollX className="max-h-[300px] px-[2px]">
        <Box my="2px">
          {options.map((option) => {
            return option.data.type === TypeaheadMenuOptionType.header
              ? (
                  <span>UNKNOWN</span>
                )
              : option.data.type === TypeaheadMenuOptionType.file
                || option.data.type === TypeaheadMenuOptionType.folder
                || option.data.type === TypeaheadMenuOptionType.rule
                ? (
                    <MenuItem_File
                      key={option.index}
                      data={option.data}
                      index={option.index}
                    />
                  )
                : option.data.type === TypeaheadMenuOptionType.heading
                  ? (
                      <MenuItem_Heading
                        key={option.index}
                        data={option.data}
                        index={option.index}
                        disabled={false}
                        disabledMsg=""
                      />
                    )
                  : (
                      <div>
                        {option.data.type}
                        ???
                      </div>
                    );
          })}
        </Box>
      </CustomScrollBar>
      {!options.length && <EmptyText subMenuTitle="空" isSecondary={false} />}
    </>
  );
}

export function MenuList() {
  const context = useContext(MentionPanelContext);
  if (!context) {
    throw new Error("NewPanelContext is not provided");
  }
  const { mentionType: menuType } = context;

  const { currentMenu } = useTypeaheadMenuContext();

  return menuType === RichEditorMenuType.SLASH_COMMAND
    && currentMenu === TypeaheadMenuOptionType.customPrompt
    ? (
        <MenuListCustomPrompt />
      )
    : (
        <MenuListCommon />
      );
}
