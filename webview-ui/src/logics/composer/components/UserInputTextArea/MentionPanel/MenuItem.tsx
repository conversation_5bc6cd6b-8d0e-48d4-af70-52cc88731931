import clsx from "clsx";
import {
  forwardRef,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useMemo,
} from "react";
import { HighlightText } from "./Highlight";
import AutoTooltip from "@/components/AutoTooltip";
import { MentionTypeaheadOption, TypeaheadMenuOptionType } from "./useOptions";
import { MentionPanelContext, useMentionPanelContext } from "./MentionPanel";
import { Spinner, useMergeRefs } from "@chakra-ui/react";
import IconDir from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_warehouse_line";
import KidIcon from "@/components/Union/kid";
import { useTypeaheadMenuContext } from "@/components/TextArea/lexical/MentionsV2Plugin";
import { Icon } from "@/components/Union/t-iconify";
import { getIcon } from "@/utils/fileIcon";
import {
  MentionNodeV2Structure_File,
  MentionNodeV2Structure_Rule,
  MentionNodeV2Structure_Tree,
} from "shared/lib/MentionNodeV2/nodes";
import IconCorrect from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_correct";

import IconArrowLeft from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_smallarrow_left";
import { useContextHeaderContext } from "../ContextHeader/ContextHeaderContext";
import { FilenameDisplay } from "@/logics/composer/tools/components/FilenameDisplay";
import { ConfigProvider } from "antd";
import { ReportOpt } from "@shared/types/logger";
import { reportUserAction } from "@/utils/weblogger";

export interface MenuItemProps {
  data: MentionTypeaheadOption;
  index: number;
  disabled: boolean;
  disabledMsg: string;
  children?: React.ReactNode;
}

export const MenuItem = forwardRef<HTMLButtonElement, MenuItemProps>(
  function MenuItem(
    { data, index, disabled, disabledMsg, children },
    forwardedRef,
  ) {
    const option = data;
    const context = useContext(MentionPanelContext);
    if (!context) {
      throw new Error("MentionPanelContext is not provided");
    }
    const { handleSelectMenu, query, selectIndex, submittingIndex } = context;
    const {
      itemProps: { setHighlightedIndex },
    } = useTypeaheadMenuContext();

    const isSecondary = false;

    const selected = index === selectIndex;

    const menuRef = useRef<HTMLButtonElement>(null);

    const composedRef = useMergeRefs(menuRef, forwardedRef);

    useEffect(() => {
      if (selected && menuRef.current) {
        menuRef.current.scrollIntoView({
          block: "nearest",
          behavior: "instant",
        });
      }
    }, [selected]);

    const handleHoverSelect = useCallback(() => {
      setHighlightedIndex(index);
    }, [index, setHighlightedIndex]);

    return (
      <button
        ref={composedRef}
        style={{
          /* FIXME: --vscode-font-family 是否应当在全局设置 */
          fontFamily: isSecondary ? "var(--vscode-font-family)" : "inherit",
        }}
        className={clsx(
          `group flex  items-center  px-3  w-full rounded-sm`,
          [isSecondary ? "h-[28px]" : "h-[36px]"],
          [isSecondary ? "leading-[16px]" : "leading-[19.5px]"],
          [selected ? "bg-list-hoverBackground" : ""],
          [disabled ? "cursor-not-allowed" : "cursor-pointer"],
        )}
        disabled={disabled}
        onClick={() => handleSelectMenu(data, index)}
        onMouseMove={handleHoverSelect}
        onMouseDown={(e) => {
          e.preventDefault();
        }}
      >
        {children || (
          <>
            <div
              className={clsx("overflow-hidden  mr-6 flex items-center", [
                disabled
                  ? "text-text-common-disable"
                  : "text-text-common-primary",
              ])}
            >
              <HighlightText text={option.name} highlight={query} />
            </div>
            <div
              className={clsx(
                "kwaipilot-rich-editor-menu-description",
                [
                  disabled
                    ? ""
                    : selected
                      ? "flex"
                      : isSecondary
                        ? ""
                        : "hidden",
                ],
                "flex-auto overflow-hidden",
                [
                  disabled
                    ? "text-text-common-disable"
                    : "text-text-common-secondary",
                ],
                ["justify-end", "flex"],
              )}
            >
              <AutoTooltip title={option.secondaryText} placement="top">
                {disabled ? disabledMsg : option.secondaryText}
              </AutoTooltip>
            </div>
            {submittingIndex === index && <Spinner ml="4px" size="xs" />}
          </>
        )}
      </button>
    );
  },
);

const OPTION_TYPE_LABELS: Record<TypeaheadMenuOptionType, string> = {
  [TypeaheadMenuOptionType.none]: "",
  [TypeaheadMenuOptionType.heading]: "heading",
  [TypeaheadMenuOptionType.header]: "header",
  [TypeaheadMenuOptionType.file]: "文件",
  [TypeaheadMenuOptionType.folder]: "目录",
  [TypeaheadMenuOptionType.rule]: "规则",
  [TypeaheadMenuOptionType.customPrompt]: "自定义指令",
  [TypeaheadMenuOptionType.addRule]: "添加规则",
};

function IconFile() {
  return (
    <svg
      width="12"
      height="12"
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.148 3.7275L7.52297 1.1025C7.47073 1.0502 7.40869 1.0087 7.34041 0.98039C7.27212 0.952078 7.19892 0.937503 7.125 0.9375H2.625C2.37636 0.9375 2.1379 1.03627 1.96209 1.21209C1.78627 1.3879 1.6875 1.62636 1.6875 1.875V10.125C1.6875 10.3736 1.78627 10.6121 1.96209 10.7879C2.1379 10.9637 2.37636 11.0625 2.625 11.0625H9.375C9.62364 11.0625 9.8621 10.9637 10.0379 10.7879C10.2137 10.6121 10.3125 10.3736 10.3125 10.125V4.125C10.3125 3.97593 10.2533 3.83296 10.148 3.7275ZM7.5 2.67188L8.57812 3.75H7.5V2.67188ZM2.8125 9.9375V2.0625H6.375V4.3125C6.375 4.46168 6.43426 4.60476 6.53975 4.71025C6.64524 4.81574 6.78832 4.875 6.9375 4.875H9.1875V9.9375H2.8125ZM8.0625 6.1875C8.0625 6.33668 8.00324 6.47976 7.89775 6.58525C7.79226 6.69074 7.64918 6.75 7.5 6.75H4.5C4.35082 6.75 4.20774 6.69074 4.10225 6.58525C3.99676 6.47976 3.9375 6.33668 3.9375 6.1875C3.9375 6.03832 3.99676 5.89524 4.10225 5.78975C4.20774 5.68426 4.35082 5.625 4.5 5.625H7.5C7.64918 5.625 7.79226 5.68426 7.89775 5.78975C8.00324 5.89524 8.0625 6.03832 8.0625 6.1875ZM8.0625 8.0625C8.0625 8.21168 8.00324 8.35476 7.89775 8.46025C7.79226 8.56574 7.64918 8.625 7.5 8.625H4.5C4.35082 8.625 4.20774 8.56574 4.10225 8.46025C3.99676 8.35476 3.9375 8.21168 3.9375 8.0625C3.9375 7.91332 3.99676 7.77024 4.10225 7.66475C4.20774 7.55926 4.35082 7.5 4.5 7.5H7.5C7.64918 7.5 7.79226 7.55926 7.89775 7.66475C8.00324 7.77024 8.0625 7.91332 8.0625 8.0625Z"
        fill="currentColor"
      />
    </svg>
  );
}

export const MenuItem_File = forwardRef<
  HTMLButtonElement,
  {
    data: MentionTypeaheadOption;
    index: number;
  }
>(function MenuItem_File({ data, index }, forwardedRef) {
  const context = useContext(MentionPanelContext);
  const { state: contextHeaderState } = useContextHeaderContext();
  if (!context) {
    throw new Error("MentionPanelContext is not provided");
  }
  const { handleSelectMenu, query, selectIndex, submittingIndex } = context;
  const {
    itemProps: { setHighlightedIndex },
  } = useTypeaheadMenuContext();

  const selected = index === selectIndex;

  const rootRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (selected) {
      rootRef.current?.scrollIntoView({
        behavior: "instant",
        block: "nearest",
      });
    }
  }, [selected]);

  const isInContextHeader = useMemo(() => {
    if (
      [
        TypeaheadMenuOptionType.file,
        TypeaheadMenuOptionType.folder,
        TypeaheadMenuOptionType.rule,
      ].includes(data.type)
      && data.structure
    ) {
      const structure = data.structure as
        | MentionNodeV2Structure_File
        | MentionNodeV2Structure_Tree
        | MentionNodeV2Structure_Rule;
      return !!contextHeaderState.findNode(structure);
    }
    return false;
  }, [data, contextHeaderState]);

  const handleHoverSelect = useCallback(() => {
    setHighlightedIndex(index);
  }, [index, setHighlightedIndex]);

  const fileIcon = useMemo(() => {
    if (data.type === TypeaheadMenuOptionType.file && data.structure) {
      const structure = data.structure as MentionNodeV2Structure_File;
      return getIcon(structure.relativePath, false);
    }
    else if (data.type === TypeaheadMenuOptionType.folder && data.structure) {
      const structure = data.structure as MentionNodeV2Structure_Tree;
      return getIcon(structure.relativePath, true);
    }
    else if (data.type === TypeaheadMenuOptionType.rule && data.structure) {
      return "lucide:file-code";
    }
    return "vscode-icons:file-type-text";
  }, [data]);

  const { selectMode } = useMentionPanelContext();

  return (
    <button
      ref={useMergeRefs(rootRef, forwardedRef)}
      style={{
        fontFamily: "var(--vscode-font-family)",
      }}
      className={clsx(
        "group flex items-center px-3 gap-1 w-full rounded-sm h-[28px] leading-[16px] cursor-pointer focus-visible:outline-none",
        selected
          ? "bg-list-hoverBackground"
          : "",
      )}
      onClick={() => handleSelectMenu(data, index)}
      onMouseMove={handleHoverSelect}
      onMouseDown={(e) => {
        e.preventDefault();
      }}
    >
      <Icon icon={fileIcon} className="text-[14px] flex-none" />
      <div
        className={clsx(
          "overflow-hidden mr-6 flex items-center gap-1 flex-none max-w-[156px]",
          ["text-text-common-secondary"],
        )}
      >
        <ConfigProvider
          theme={{
            components: {
              Tooltip: {
                zIndexPopup: 10000 /* > var(--chakra-zIndices-popover) */,
              },
            },
          }}
        >
          <FilenameDisplay
            highlight={query}
            filename={data.name}
            maxSuffixLenth={5}
          >
          </FilenameDisplay>
        </ConfigProvider>
      </div>
      <div
        className={clsx(
          "kwaipilot-rich-editor-menu-description flex-auto overflow-hidden text-descriptionForeground justify-end flex",
        )}
      >
        <AutoTooltip
          title={data.secondaryText || ""}
          openDelay={700}
          placement="top"
        >
          {data.secondaryText}
        </AutoTooltip>
      </div>
      {selectMode === "unique" && isInContextHeader && (
        <KidIcon
          size={12}
          color="currentColor"
          config={IconCorrect}
          className="ml-auto flex-none"
        />
      )}
      {submittingIndex === index && <Spinner ml="4px" size="xs" />}
    </button>
  );
});

export const MenuItem_Heading = forwardRef<HTMLButtonElement, MenuItemProps>(
  function MenuItem_Heading({ data, index, disabled, children }, forwardedRef) {
    const composedRef = useMergeRefs(forwardedRef);
    const context = useContext(MentionPanelContext);
    if (!context) {
      throw new Error("MentionPanelContext is not provided");
    }
    const { handleSelectMenu, query, selectIndex } = context;
    const {
      itemProps: { setHighlightedIndex },
    } = useTypeaheadMenuContext();

    const selected = index === selectIndex;

    return (
      <button
        ref={composedRef}
        className={clsx(
          `group flex  items-center  px-2 text-[12px]  w-full rounded-sm h-[26px] leading-[18px]`,
          [
            selected
              ? "bg-list-hoverBackground"
              : "",
          ],
          [disabled ? "cursor-not-allowed" : "cursor-pointer"],
        )}
        disabled={disabled}
        onClick={() => {
          handleSelectMenu(data, index);
        }}
        onMouseMove={() => {
          setHighlightedIndex(index);
        }}
        onMouseDown={(e) => {
          e.preventDefault();
        }}
      >
        {children || (
          <>
            <div
              className={clsx(
                "overflow-hidden w-full flex items-center gap-1",
                [
                  disabled
                    ? "text-text-common-disable"
                    : "",
                ],
              )}
            >
              {data.name === TypeaheadMenuOptionType.folder
                ? (
                    <KidIcon
                      size={12}
                      color="currentColor"
                      config={IconDir}
                    >
                    </KidIcon>
                  )
                : data.name === TypeaheadMenuOptionType.file
                  ? (
                      <IconFile />
                    )
                  : data.name === TypeaheadMenuOptionType.rule
                    ? (
                        <Icon icon="lucide:file-code" />
                      )
                    : null}
              <HighlightText
                text={
                  OPTION_TYPE_LABELS[data.name as TypeaheadMenuOptionType]
                  || data.name
                }
                highlight={query}
              />
              <KidIcon
                size={12}
                config={IconArrowLeft}
                className="rotate-180 ml-auto  text-text-common-disable"
              >
              </KidIcon>
            </div>
          </>
        )}
      </button>
    );
  },
);

export function MenuItem_Header({
  item,
  index,
}: {
  item: MentionTypeaheadOption<TypeaheadMenuOptionType.header>;
  index: number;
}) {
  const { selectIndex, handleSelectMenu } = useMentionPanelContext();
  const { itemProps: { setHighlightedIndex } } = useTypeaheadMenuContext();
  const handleAddRuleClick = useCallback(() => {
    handleSelectMenu(
      new MentionTypeaheadOption<TypeaheadMenuOptionType.addRule>(
        TypeaheadMenuOptionType.addRule,
        "",
        "",
        undefined,
      ),
      0,
    );
    const param: ReportOpt<"input_rules_create"> = {
      key: "input_rules_create",
      type: undefined,
      content: "new_composer",
    };
    reportUserAction(param);
  }, [handleSelectMenu]);
  return (
    <div className=" p-[3px] border-b-[0.5px] border-border-horizontal border-settings-dropdownBorder">
      <button
        className={clsx(
          "flex px-2 w-full  items-center   text-[12px] h-[26px] box-border rounded justify-between  leading-[18px]",
          selectIndex === index
            ? "bg-list-hoverBackground"
            : "",
        )}
        onMouseMove={() => {
          setHighlightedIndex(index);
        }}
        onClick={() => {
          handleSelectMenu(item, index);
        }}
      >
        <div
          className={clsx(
            "flex gap-[6px] text-text-common-primary w-full items-center font-medium leading-[19.5px]",
          )}
        >
          <KidIcon
            config={IconArrowLeft}
            size={16}
            color="currentColor"
          >
          </KidIcon>
          {item.name}
          {item.name === "规则"
            ? (
                <div
                  tabIndex={-1}
                  onClick={handleAddRuleClick}
                  className=" text-[12px] text-text-common-primary ml-auto flex items-center gap-1"
                >
                  <Icon icon="famicons:add" />
                  添加项目规则
                </div>
              )
            : (
                <div className=" text-[12px] text-text-common-tertiary scale-[.83] ml-auto flex items-center gap-1">
                  点击或
                  <svg
                    width="13"
                    height="12"
                    viewBox="0 0 13 12"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10.119 2.5C10.2415 2.50002 10.3597 2.54498 10.4512 2.62636C10.5427 2.70774 10.6012 2.81987 10.6155 2.9415L10.619 3V6.2145C10.619 7.1435 10.022 7.943 9.20703 7.997L9.11903 8H3.82653L4.97253 9.1465C5.06221 9.23648 5.11427 9.35722 5.11815 9.48419C5.12203 9.61117 5.07743 9.73486 4.99341 9.83014C4.90939 9.92542 4.79226 9.98515 4.6658 9.9972C4.53934 10.0092 4.41303 9.97271 4.31253 9.895L4.26553 9.8535L2.26553 7.8535C2.1718 7.75974 2.11914 7.63258 2.11914 7.5C2.11914 7.36742 2.1718 7.24026 2.26553 7.1465L4.26553 5.1465C4.35551 5.05683 4.47625 5.00476 4.60323 5.00089C4.7302 4.99701 4.85389 5.04161 4.94917 5.12562C5.04446 5.20964 5.10419 5.32678 5.11623 5.45324C5.12828 5.5797 5.09174 5.706 5.01403 5.8065L4.97253 5.8535L3.82653 7H9.11903C9.34853 7 9.58703 6.7115 9.61603 6.2985L9.61903 6.2145V3C9.61903 2.86739 9.67171 2.74021 9.76548 2.64645C9.85925 2.55268 9.98643 2.5 10.119 2.5Z"
                      fill="currentColor"
                    />
                  </svg>
                  返回
                </div>
              )}
        </div>
      </button>
    </div>
  );
}
