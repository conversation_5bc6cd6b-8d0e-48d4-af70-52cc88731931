import { kwai<PERSON><PERSON>tBridgeAPI } from "@/bridge";
import { MentionNode } from "@/components/TextArea/lexical/CommandNode";
import {
  isMentionNode,
  isSharpCommandMentionNode,
  SerializedMentionNode,
} from "shared/lib/MentionNode";
import { transformToPlainTextForHumanReading } from "@/components/TextArea/lexical/editorState";
import {
  collectSharpCommandLog,
  haveSharpCommand,
} from "@/components/TextArea/lexical/SharpPlugin";
import repoChatService from "@/services/repo-chat";
import { logger } from "@/utils/logger";
import { generateCustomUUID } from "@/utils/sessionUtils";
import { collectClick, reportUserAction } from "@/utils/weblogger";
import { CommandType, PromptConfig, SharpCommand } from "@shared/types";
import {
  $createParagraphNode,
  $getRoot,
  LexicalEditor,
  SerializedEditorState,
  SerializedLexicalNode,
} from "lexical";
import { useCallback, useMemo } from "react";
import {
  isCheckpointCreatedMessage,
  isTerminalMessage,
  isToolEditFileMessage,
} from "shared/lib/agent/isToolMessage";
import { useRestoreAndSendDialog } from "../HumanMessage/RestoreAndSendDialog";
import { useSendConfirmDialog } from "../HumanMessage/SendConfirmDialog";
import { usePromptTemplate } from "@/store/promptTemplate";
import { useUserStore } from "@/store/user";
import { useAsync } from "react-use";
import { useComposerState } from "../../context/ComposerStateContext";
import {
  findFirst,
  traversePreOrder,
} from "@/components/TextArea/lexical/traversal";
import { fetchSummaryConversation } from "@/http/api/summaryConversation";
import { ReportOpt } from "@shared/types/logger";
import { chatId } from "@/utils/chatId";
import { assemblePlainTextForGpt } from "shared/lib/CustomVariable/nodes";
import path from "path-browserify";
import { isCustomVariableNode } from "shared/lib/CustomVariable";
import {
  isMentionNodeV2,
  MentionNodeV2Structure,
  MentionNodeV2Structure_Selection,
} from "shared/lib/MentionNodeV2/nodes";
import { throwNeverError } from "@/utils/throwUnknownError";
import { URI } from "vscode-uri";
import {
  ContextHeaderItem,
  ContextHeaderState,
} from "./ContextHeader/ContextHeaderContext";
import { DOM } from "@/utils/dom";

/**
 * 助理模式 2.0 将 editorState 转为 prompt 生成所需的 query
 * @param richEditorState
 * @param extra
 * @param workspacePathAndRepoPath
 * @returns
 */
export function preparePromptGenerationQueryForAgentV2(
  editorState: SerializedEditorState,
  _extra: {
    promptConfig: PromptConfig[];
    language: string;
    code: string;
  },
  workspacePathAndRepoPath: { repoPath?: string; workspacePath?: string },
): {
    query: string;
    files: string[];
    dirs: string[];
    rules: string[];
    codebase: string;
  } {
  let query = "";
  const files: string[] = [];
  const dirs: string[] = [];
  const rules: string[] = [];
  let codebase = "";

  const selections: MentionNodeV2Structure_Selection[] = [];

  let baseUrl = "";

  const workspacePath = workspacePathAndRepoPath.workspacePath;
  const repoPath = workspacePathAndRepoPath.repoPath;

  if (workspacePath && repoPath) {
    if (workspacePath !== repoPath) {
      const p = path.relative(repoPath, workspacePath);
      baseUrl = p;
    }
  }
  function walk(node: SerializedLexicalNode) {
    if (isMentionNode(node) && node.commandType === CommandType.SHARP) {
      if (node.key === SharpCommand.CURRENT_FILE) {
        query += `#{file=${path.join(baseUrl, node.data)}}`;
        files.push(node.data);
      }
      else if (node.key === SharpCommand.FILE) {
        query += `#{file=${path.join(baseUrl, node.data)}}`;
        files.push(node.data);
      }
      else if (node.key === SharpCommand.RULES) {
        rules.push(node.data);
      }
      else if (node.key === SharpCommand.FOLDER) {
        query += `#{dir=${path.join(baseUrl, node.data)}}`;
        dirs.push(node.data);
      }
      else if (node.key === SharpCommand.CODEBASE) {
        query += `#{repo}`;
        codebase = node.data;
      }
    }
    else if (isMentionNodeV2(node)) {
      const { structure } = node;
      if (structure.type === "file") {
        query += `#{file=${structure.relativePath}}`;
        files.push(URI.parse(structure.uri).path);
      }
      else if (structure.type === "selection") {
        query += `#{selection=${structure.relativePath}:${structure.range.start.line}-${structure.range.end.line}}`;
        selections.push(structure);
      }
      else if (structure.type === "tree") {
        query += `#{dir=${structure.relativePath}}`;
        dirs.push(URI.parse(structure.uri).path);
      }
      else if (structure.type === "rule") {
        rules.push(structure.relativePath);
      }
      else {
        throwNeverError(structure);
      }
    }
    else if (isCustomVariableNode(node)) {
      query += assemblePlainTextForGpt(node);
    }
    else if ("text" in node) {
      query += node.text;
    }
    if ("children" in node && Array.isArray(node.children)) {
      for (const child of node.children) {
        walk(child);
      }
    }
  }
  walk(editorState.root);

  if (selections.length > 0) {
    query
      += `\n`
      + selections
        .map(
          selection => `
<selection_content>
  <selection_path>${selection.relativePath}</selection_path>
  <selection_range>L${selection.range.start.line}-L${selection.range.end.line}</selection_range>
  <selection_content>
${selection.content}
  </selection_content>
</selection_content>
`,
        )
        .join("\n");
  }

  return {
    query,
    files,
    dirs,
    codebase,
    rules,
  };
}

export function useSubmit({
  isBlank,
  editor,
  contextHeaderState,
  role,
}: {
  isBlank: boolean;
  editor: LexicalEditor | null;
  contextHeaderState: ContextHeaderState;
  role: "bottom" | "conversation";
}) {
  const {
    isStreaming,
    editingMessageTs,
    localMessages,
    isCurrentWorkspaceSession,
    sessionId,
  } = useComposerState();

  const repoPathAndWorkspacePath = useAsync(() =>
    repoChatService.getWorkspacePathAndRepoPath(),
  );
  const userInfo = useUserStore(state => state.userInfo);

  const { invoke: invokeRestoreAndSendDialog } = useRestoreAndSendDialog();
  const { invoke: invokeSendConfirmDialog } = useSendConfirmDialog();

  const promptTemplate = usePromptTemplate();

  const relatedCheckpointMessage = useMemo(() => {
    if (!editingMessageTs) {
      return null;
    }
    const humanMessageI = localMessages.findIndex(
      v => v.ts === editingMessageTs,
    );
    if (humanMessageI === -1) {
      return null;
    }
    const prediction = localMessages[humanMessageI + 1];
    if (prediction && isCheckpointCreatedMessage(prediction)) {
      return prediction;
    }
    return null;
  }, [editingMessageTs, localMessages]);

  const { value: workspaceFileUri } = useAsync(
    () => kwaiPilotBridgeAPI.extensionComposer.$getWorkspaceFile(),
    [],
  );
  const isVscodeWorkspace = useMemo(() => {
    return Boolean(workspaceFileUri);
  }, [workspaceFileUri]);

  const shouldShowRestoreDialogBeforeSubmit = useMemo(() => {
    if (isVscodeWorkspace) {
      /* vscode 不支持回退 */
      return false;
    }
    if (role === "bottom") {
      // 现在没有重做功能了
      return false;
    }
    if (!editingMessageTs) {
      return false;
    }
    if (!isCurrentWorkspaceSession) {
      return false;
    }

    const humanMessageI = localMessages.findIndex(
      v => v.ts === editingMessageTs,
    );
    if (humanMessageI === -1) {
      return false;
    }
    const toBeRestoredMessages = localMessages.slice(humanMessageI + 1);
    const haveEditMessage = toBeRestoredMessages.some(
      v => isToolEditFileMessage(v) || isTerminalMessage(v),
    );
    return Boolean(relatedCheckpointMessage) && haveEditMessage;
  }, [
    editingMessageTs,
    isCurrentWorkspaceSession,
    isVscodeWorkspace,
    localMessages,
    relatedCheckpointMessage,
    role,
  ]);

  const disabled = !isStreaming && isBlank;

  const onSubmit = useCallback(async (): Promise<boolean> => {
    collectClick("VS_SUBMIT_BUTTON");
    if (!editor) {
      return false;
    }

    const editorState = editor.getEditorState().toJSON();
    if (!editorState || !editor) {
      kwaiPilotBridgeAPI.showToast({
        level: "error",
        message: "编辑器未初始化, 请重试",
      });
      return false;
    }
    if (!userInfo) {
      return false;
    }
    const uniqueId
      = Date.now().toString(36) + Math.random().toString(36).substr(2);
    chatId.updateChatId(uniqueId);

    const extraInfo = {
      startLine: 0,
      endLine: 0,
      language: "",
      filename: "",
      code: "",
    };

    const slashCommand = findFirst(
      editorState,
      (node): node is SerializedMentionNode =>
        isMentionNode(node) && node.commandType === CommandType.SLASH,
    );

    const questionForHumanReading
      = transformToPlainTextForHumanReading(editorState);

    if (slashCommand) {
      // 如果 editorState 中有 slash 命令，则返回一个简化的 editorState
      editor.update(
        () => {
          const root = $getRoot();
          root.clear();
          const p = $createParagraphNode();
          root.append(p);
          const commandNode = MentionNode.importJSON(slashCommand);
          console.log(commandNode);
        },
        { discrete: true },
      );
    }
    // 把提问和回答消息放进数组用于展示

    const promptGenerationQuery = preparePromptGenerationQueryForAgentV2(
      editorState,
      {
        promptConfig: promptTemplate.promptTemplate || [],
        language: extraInfo.language,
        code: extraInfo.code,
      },
      repoPathAndWorkspacePath.value || {
        workspacePath: "",
        repoPath: "",
      },
    );

    const formatQuestion = promptGenerationQuery.query;
    const promptRules = promptGenerationQuery.rules;
    const contextRules = contextHeaderState.nodes
      .filter(node => node.structure.type === "rule")
      .map(node => node.structure.relativePath);
    const rules = [...new Set([...promptRules, ...contextRules])];

    const targetSessionId
      = sessionId || localMessages[0]?.sessionId || generateCustomUUID();

    let title = "";
    fetchSummaryConversation(questionForHumanReading, (chunk) => {
      title += chunk;
      kwaiPilotBridgeAPI.updateComposerSessionName({
        sessionId: targetSessionId,
        name: title,
      });
    });

    // 立即滚动到底部
    const dialogContainer = DOM.$(".chat-dialog-container");
    if (dialogContainer) {
      dialogContainer.scrollTo({
        top: 0,
        behavior: "instant",
      });
    }

    const haveSharp = haveSharpCommand(editorState);
    if (haveSharp) {
      /**
       * hack: 修复一个添加历史记录时选中项可能是空的情况
       */

      repoChatService.updateCodeSearchSelectHistory({
        file: promptGenerationQuery.files.filter(i => i.trim() !== ""),
        dir: promptGenerationQuery.dirs.filter(i => i.trim() !== ""),
      });

      logger.info("get code search select item", "code-search", {
        value: {
          files: promptGenerationQuery.files,
          dirs: promptGenerationQuery.dirs,
          query: promptGenerationQuery.query,
          codebase: promptGenerationQuery.codebase,
        },
      });
    }

    const sharpCommandLog = collectSharpCommandLog(editorState);

    /** 上报埋点 /指令 #知识库 */
    if (sharpCommandLog) {
      const param: ReportOpt<"codeSearch"> = {
        key: "codeSearch",
        type: sharpCommandLog,
      };
      reportUserAction(param);
    }

    const contextLog = collectContextLog(editorState, contextHeaderState.nodes);
    if (contextLog) {
      const param: ReportOpt<"input_context_send"> = {
        key: "input_context_send",
        type: contextLog,
        content: "new_composer",
      };
      reportUserAction(param);
    }

    const conversationId = generateCustomUUID();

    kwaiPilotBridgeAPI.extensionComposer.$postMessageToComposerEngine({
      type: "newTask",
      task: formatQuestion,
      reqData: {
        sessionId: targetSessionId,
        chatId: conversationId,
      },
      rules,
      editorState: editor.getEditorState().toJSON(),
      questionForHumanReading: questionForHumanReading,
      // TODO: context info是否拆开
      contextItems: contextHeaderState.nodes.map(node => node.structure),
      editingMessageTs,
    });

    return true;

    // 发送给extension
  }, [
    editor,
    userInfo,
    promptTemplate.promptTemplate,
    repoPathAndWorkspacePath.value,
    sessionId,
    localMessages,
    contextHeaderState.nodes,
    editingMessageTs,
  ]);

  const onSubmitPrecheck = useCallback(async (): Promise<boolean> => {
    if (disabled) {
      return false;
    }
    if (isStreaming) {
      if (!isBlank) {
        kwaiPilotBridgeAPI.showToast({
          level: "error",
          message: "正在生成回答，请稍后尝试",
        });
      }
      return false;
    }
    if (shouldShowRestoreDialogBeforeSubmit) {
      const result = await invokeRestoreAndSendDialog();
      if (result === "restoreAndSend") {
        if (!relatedCheckpointMessage?.lastCheckpointHash) {
          kwaiPilotBridgeAPI.showToast({
            message: "没有找到相关的检查点",
            level: "error",
          });
          return false;
        }
        if (!editingMessageTs) {
          return false;
        }
        await kwaiPilotBridgeAPI.extensionComposer.$restoreCheckpoint({
          humanMessageTs: editingMessageTs,
          restoreCommitHash: relatedCheckpointMessage.lastCheckpointHash,
          updateStateImmediately: false,
        });
        return true;
      }
      else if (result === "keepAndSend") {
        if (!editingMessageTs) {
          return false;
        }
        await kwaiPilotBridgeAPI.extensionComposer.$revertHistory({
          humanMessageTs: editingMessageTs,
          updateStateImmediately: false,
        });
        return true;
      }
    }
    else if (editingMessageTs) {
      const result = await invokeSendConfirmDialog();
      if (result === "confirm") {
        await kwaiPilotBridgeAPI.extensionComposer.$revertHistory({
          humanMessageTs: editingMessageTs,
          updateStateImmediately: false,
        });
        return true;
      }
    }
    else {
      return true;
    }
    return false;
  }, [
    disabled,
    isStreaming,
    shouldShowRestoreDialogBeforeSubmit,
    editingMessageTs,
    isBlank,
    invokeRestoreAndSendDialog,
    relatedCheckpointMessage?.lastCheckpointHash,
    invokeSendConfirmDialog,
  ]);

  const doSubmit = useCallback(async () => {
    const preCheckResult = await onSubmitPrecheck();
    if (!preCheckResult) {
      return { result: false };
    }
    const res = await onSubmit();
    return { result: res };
  }, [onSubmitPrecheck, onSubmit]);

  return {
    doSubmit,
  };
}

/**
 * 从用户输入中获取其使用 #指令的记录
 *
 * 包括 旧版的 sharp command(兼容普通对话) 和新版的 mention context
 */
export function collectContextLog(
  editorState: SerializedEditorState,
  contextItems: ContextHeaderItem[],
): string {
  const commands = new Set<string>();
  const LEGACY_SHARP_COMMAND_LOG_MAP: Record<SharpCommand, string> = {
    [SharpCommand.CURRENT_FILE]: "currentFile",
    [SharpCommand.FILE]: "file",
    [SharpCommand.FOLDER]: "dir",
    [SharpCommand.CODEBASE]: "codebase",
    [SharpCommand.RULES]: "rule",
  };
  const CONTEXT_TYPE_MAP: Record<MentionNodeV2Structure["type"], string> = {
    file: "file",
    rule: "rule",
    tree: "dir",
    selection: "selection",
  };
  for (const node of traversePreOrder(editorState.root)) {
    if (isMentionNode(node) && isSharpCommandMentionNode(node)) {
      commands.add(LEGACY_SHARP_COMMAND_LOG_MAP[node.key as SharpCommand]);
    }
  }
  for (const item of contextItems) {
    if (item.followActiveEditor) {
      commands.add("currentFile");
    }
    else {
      commands.add(CONTEXT_TYPE_MAP[item.structure.type]);
    }
  }
  return Array.from(commands).join(",");
}
