import { Box, BoxProps } from "@chakra-ui/react";
import { LexicalEditor } from "lexical";
import { useEffect, useMemo, useCallback } from "react";
import { ContextHeaderItem, isIdentical, mentionNodeUniqueKey, useContextHeaderContext } from "./ContextHeaderContext";
import { produce } from "immer";
import { MentionEditButton } from "./MentionEditButton";
import { MentionNodeV2Structure, MentionNodeV2Structure_File } from "shared/lib/MentionNodeV2/nodes";
import { useLatest } from "react-use";
import { useBridgeObservableAPI } from "@/bridge/useBridgeObservableAPI";
import { MentionNodeLabelLayout } from "./MentionNodeLabelLayout";
import { collectMentionNodeV2 } from "./collectMentionNode";
import { isRuleFile } from "shared/lib/util";
import { MentionNodeLabel, MentionNodeLabel2, MentionNodeLabelReadonly, MentionNodeLabelReadonly2 } from "./MentionNodeLabel";

export function ContextHeader({
  editor,
  disabledCurrentFileBinding,
  ...rest
}: {
  editor: LexicalEditor;
  disabledCurrentFileBinding?: boolean;
} & BoxProps) {
  const { state: { nodes, setNodes, markAsModified } } = useContextHeaderContext();
  const followActiveEditorNode = useMemo(() => {
    return nodes.find(node => node.followActiveEditor);
  }, [nodes]);
  const restNodes = useMemo(() => {
    return nodes.filter(node => !node.followActiveEditor);
  }, [nodes]);

  const nodesRef = useLatest(nodes);

  const updateContextNodes = useCallback(() => {
    let shouldMarkAsModified = false;
    let draftModified = false;

    const frameId = requestAnimationFrame(() => {
      const editorNodes = collectMentionNodeV2(editor);
      const modifiedNodes = produce(nodesRef.current, (draft) => {
        const toBeDeleted: ContextHeaderItem[] = [];
        // 尝试删除虚拟节点
        for (const node of draft.filter(n => n.isVirtualContext)) {
          if (!editorNodes.some(n => isIdentical(n.__structure, node.structure))) {
            toBeDeleted.push(node);
            draftModified = true;
          }
        }
        const toBeAdded: ContextHeaderItem[] = [];
        for (const editorNode of editorNodes) {
          // 尝试合并
          const target = draft.find(v => isIdentical(v.structure, editorNode.__structure));
          if (!target) {
            toBeAdded.push({
              structure: editorNode.__structure,
              followActiveEditor: false,
              isVirtualContext: true,
            });
            draftModified = true;
            // 如果有新增节点，标记为已修改
            shouldMarkAsModified = true;
          }
        }
        for (const node of toBeDeleted) {
          draft.splice(draft.indexOf(node), 1);
        }
        for (const node of toBeAdded) {
          draft.push(node);
        }
      });
      if (draftModified) {
        setNodes(modifiedNodes);
      }

      if (shouldMarkAsModified) {
        markAsModified();
      }
    });

    return () => {
      cancelAnimationFrame(frameId);
    };
  }, [editor, nodesRef, setNodes, markAsModified]);

  useEffect(() => {
    const cancel = updateContextNodes();
    return () => {
      cancel?.();
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    editor.registerTextContentListener(() => {
      updateContextNodes();
    });
  }, [editor, updateContextNodes]);

  const currentFileAndSelection = useBridgeObservableAPI("currentFileAndSelection");

  // 初始带入一个当前文件
  useEffect(() => {
    if (disabledCurrentFileBinding) {
      return;
    }
    if (!currentFileAndSelection || isRuleFile(currentFileAndSelection.relativePath)) {
      return;
    }
    const followActiveEditorNodeI = nodes.findIndex(v => v.followActiveEditor);
    const newestStructure: MentionNodeV2Structure_File | null = {
      type: "file",
      uri: currentFileAndSelection.uri,
      relativePath: currentFileAndSelection.relativePath,
    };
    if (followActiveEditorNodeI > -1 && !isIdentical(nodes[followActiveEditorNodeI].structure, newestStructure)) {
      setNodes(prev => produce(prev, (draft) => {
        draft.splice(followActiveEditorNodeI, 1, {
          structure: newestStructure,
          followActiveEditor: true,
          isVirtualContext: false,
        });
      }));
    }
  }, [currentFileAndSelection, disabledCurrentFileBinding, nodes, setNodes]);

  return (
    <Box position="relative" p={3} pb="6px" {...rest}>
      <MentionNodeLabelLayout
        before={(
          <>
            <MentionEditButton editor={editor} />
            {followActiveEditorNode && (
              <MentionNodeLabel
                onDelete={() => {
                  markAsModified();
                }}
                node={followActiveEditorNode}
                editor={editor}
              />
            )}
          </>
        )}
        list={restNodes}
        renderItem={node => (
          <MentionNodeLabel key={mentionNodeUniqueKey(node.structure)} node={node} editor={editor} />
        )}
        renderRestItem={node => (
          <MentionNodeLabel2 key={mentionNodeUniqueKey(node.structure)} node={node} editor={editor} />
        )}
      />
    </Box>
  );
}

export function ContextHeaderReadonly({
  persistentNodes,
  ...rest
}: {
  persistentNodes: MentionNodeV2Structure[];
} & BoxProps) {
  return (
    <Box position="relative" {...rest}>
      <MentionNodeLabelLayout<MentionNodeV2Structure>
        before={null}
        list={persistentNodes}
        renderItem={item => (
          <MentionNodeLabelReadonly key={mentionNodeUniqueKey(item)} structure={item} />
        )}
        renderRestItem={item => (
          <MentionNodeLabelReadonly2 key={mentionNodeUniqueKey(item)} structure={item} />
        )}
      />
    </Box>
  );
}
