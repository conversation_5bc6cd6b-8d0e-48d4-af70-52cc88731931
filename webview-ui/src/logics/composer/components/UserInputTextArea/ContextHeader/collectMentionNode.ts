import { $getRoot, $isElementNode, LexicalEditor, LexicalNode } from "lexical";
import { $isMentionNodeV2, MentionNodeV2 } from "@/components/TextArea/lexical/MentionNodeV2/MentionNodeV2";

export function collectMentionNodeV2(editor: LexicalEditor) {
  const result: MentionNodeV2[] = [];
  editor.read(() => {
    const walk = (node: LexicalNode) => {
      if ($isMentionNodeV2(node)) {
        result.push(node);
      }
      else if ($isElementNode(node)) {
        node.getChildren().forEach(child => walk(child));
      }
    };
    walk($getRoot());
  });
  return result;
}
