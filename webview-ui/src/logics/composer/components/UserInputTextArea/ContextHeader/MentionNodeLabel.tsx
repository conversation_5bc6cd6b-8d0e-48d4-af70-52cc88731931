import { getIcon } from "@/utils/fileIcon";
import { Tooltip } from "@/components/Union/chakra-ui";
import { Box, Flex, useSize } from "@chakra-ui/react";
import { Icon } from "@/components/Union/t-iconify";
import { LexicalEditor } from "lexical";
import { useMemo, useRef } from "react";
import { basename } from "path-browserify";
import KidIcon from "@/components/Union/kid";

import IconClose from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_close";
import { ContextHeaderItem, isIdentical, useContextHeaderContext } from "./ContextHeaderContext";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { ConfigProvider } from "antd";
import { MentionNodeV2Structure } from "shared/lib/MentionNodeV2/nodes";
import { displayLineRange } from "shared/lib/CustomVariable/cody-shared/range";
import { FilenameDisplay } from "@/logics/composer/tools/components/FilenameDisplay";
import { collectMentionNodeV2 } from "./collectMentionNode";

function useMentionNodeLabelOperation({
  editor,
  node,
  onDelete,
}: {
  editor: LexicalEditor;
  node: ContextHeaderItem;
  onDelete?: () => void;
}) {
  const { state: { tryDeleteNode } } = useContextHeaderContext();
  const onCloseClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    // 如果有关联的 editor 节点，先删除它
    const matchedNodes = collectMentionNodeV2(editor).filter(n => isIdentical(n.__structure, node.structure));
    if (matchedNodes.length > 0) {
      editor.update(() => {
        for (const matchedNode of matchedNodes) {
          matchedNode.remove();
        }
      });
    }

    // 更新 context state
    tryDeleteNode(node.structure);
    onDelete?.();
  };
  return {
    onCloseClick,
  };
}

export function MentionNodeLabel({ node, editor, onDelete }: {
  node: ContextHeaderItem;
  editor: LexicalEditor;
  onDelete?: () => void;
}) {
  const { relativePath, type } = node.structure;

  const filename = basename(relativePath);

  const rangeRef = useRef<HTMLSpanElement>(null);

  const rangeRefSize = useSize(rangeRef);
  const iconType = useMemo(() => {
    if (type === "rule") {
      return "lucide:file-code";
    }
    else {
      return getIcon(relativePath, type === "tree");
    }
  }, [relativePath, type]);

  const { onCloseClick } = useMentionNodeLabelOperation({
    editor,
    node,
    onDelete,
  });

  const maxSuffixLength = node.structure.type === "selection" ? (Math.max(15 - displayLineRange(node.structure.range).length, 0)) : 15;

  return (
    <Tooltip label={relativePath} openDelay={700} placement="top">
      <Flex
        align="center"
        display="inline-flex"
        gap={1}
        userSelect="none"
        fontFamily="var(--vscode-font-family)"
        className="group max-w-[160px] h-[22px] flex-none  cursor-pointer pl-[4px] pr-[6px] border border-solid border-commandCenter-inactiveBorder rounded hover:bg-toolbar-hoverBackground"
        onClick={() => {
          kwaiPilotBridgeAPI.extensionComposer.$locateMentionNodeV2(node.structure);
        }}
        overflow="hidden"
      >
        <div className=" float-none flex items-center size-[14px]">
          <Icon icon={iconType} className=" group-hover:hidden size-[14px]" />
          <KidIcon onClick={onCloseClick} config={IconClose} className=" flex-none group-hover:block hidden size-[14px] text-[var(--custom-text-common)]" />
        </div>
        <Box maxW={`${120 - (rangeRefSize?.width || 0)}px`}>
          <FilenameDisplay tooltip={false} filename={filename} maxSuffixLenth={maxSuffixLength} />
        </Box>
        {node.structure.type === "selection" && (
          <span ref={rangeRef} className=" flex-none  text-[12px] text-[var(--custom-text-common)]">
            {displayLineRange(node.structure.range)}
          </span>
        )}
      </Flex>
    </Tooltip>
  );
}

export function MentionNodeLabel2({ node, editor, onDelete }: {
  node: ContextHeaderItem;
  editor: LexicalEditor;
  onDelete?: () => void;
}) {
  const { relativePath, type } = node.structure;

  const filename = basename(relativePath);

  const rangeRef = useRef<HTMLSpanElement>(null);

  const rangeRefSize = useSize(rangeRef);

  const { onCloseClick } = useMentionNodeLabelOperation({
    editor,
    node,
    onDelete,
  });

  return (
    <ConfigProvider
      theme={{
        components: {
          Tooltip: {
            zIndexPopup: 10000/* > var(--chakra-zIndices-popover) */,
          },
        },
      }}
    >
      <Tooltip label={relativePath} openDelay={700} closeOnScroll placement="top">
        <Flex
          align="center"
          display="inline-flex"
          gap={1}
          userSelect="none"
          fontFamily="var(--vscode-font-family)"
          className="group w-full h-[26px] flex-none  cursor-pointer px-[8px] rounded hover:bg-list-hoverBackground"
          onClick={() => {
            kwaiPilotBridgeAPI.extensionComposer.$locateMentionNodeV2(node.structure);
          }}
          position="relative"
        >
          <Icon icon={getIcon(relativePath, type === "tree")} className=" flex-none size-[14px]" />
          <Box maxW={`${140 - (rangeRefSize?.width || 0)}px`} w="full">
            <FilenameDisplay tooltip={false} className=" w-full" maxSuffixLenth={6} filename={filename} />
          </Box>
          {node.structure.type === "selection" && (
            <span ref={rangeRef} className=" flex-none  text-[12px]  text-descriptionForeground">
              {displayLineRange(node.structure.range)}
            </span>
          )}
          <KidIcon onClick={onCloseClick} config={IconClose} className="absolute right-[6px] group-hover:opacity-100 opacity-0 size-[14px] text-icon-foreground" />
        </Flex>
      </Tooltip>
    </ConfigProvider>
  );
}

export function MentionNodeLabelReadonly({ structure }: {
  structure: MentionNodeV2Structure;
}) {
  const { relativePath, type } = structure;

  const filename = basename(relativePath);

  const rangeRef = useRef<HTMLSpanElement>(null);

  const rangeRefSize = useSize(rangeRef);

  const iconType = useMemo(() => {
    if (type === "rule") {
      return "lucide:file-code";
    }
    else {
      return getIcon(relativePath, type === "tree");
    }
  }, [relativePath, type]);

  return (
    <Tooltip label={relativePath} openDelay={700} placement="top">
      <Flex
        align="center"
        display="inline-flex"
        gap={1}
        userSelect="none"
        fontFamily="var(--vscode-font-family)"
        className="group max-w-[160px] h-[22px] flex-none  cursor-pointer  pl-[4px] pr-[6px] border border-solid border-commandCenter-inactiveBorder rounded hover:bg-toolbar-hoverBackground"
        onClick={() => {
          kwaiPilotBridgeAPI.extensionComposer.$locateMentionNodeV2(structure);
        }}
      >
        <div className="flex-none size-[14px] flex items-center">
          <Icon icon={iconType} className=" size-[14px]" />
        </div>
        <Box maxW={`${130 - (rangeRefSize?.width || 0)}px`}>
          <FilenameDisplay tooltip={false} filename={filename} maxSuffixLenth={10} />
        </Box>

        {structure.type === "selection" && (
          <span ref={rangeRef} className=" flex-none  text-[12px]  text-[var(--custom-text-common)]">
            {displayLineRange(structure.range)}
          </span>
        )}
      </Flex>
    </Tooltip>
  );
}

export function MentionNodeLabelReadonly2({ structure }: {
  structure: MentionNodeV2Structure;
}) {
  const { relativePath, type } = structure;

  const filename = basename(relativePath);

  const rangeRef = useRef<HTMLSpanElement>(null);

  const rangeRefSize = useSize(rangeRef);

  return (
    <ConfigProvider
      theme={{
        components: {
          Tooltip: {
            zIndexPopup: 10000/* > var(--chakra-zIndices-popover) */,
          },
        },
      }}
    >
      <Tooltip label={relativePath} openDelay={700} closeOnScroll placement="top">
        <Flex
          align="center"
          display="inline-flex"
          gap={1}
          userSelect="none"
          fontFamily="var(--vscode-font-family)"
          className="group w-full h-[26px] flex-none  cursor-pointer px-[8px] rounded hover:bg-toolbar-hoverBackground"
          onClick={() => {
            kwaiPilotBridgeAPI.extensionComposer.$locateMentionNodeV2(structure);
          }}
        >
          <Icon icon={getIcon(relativePath, type === "tree")} className=" flex-none size-[14px]" />
          <Box maxW={`${140 - (rangeRefSize?.width || 0)}px`}>
            <FilenameDisplay tooltip={false} className=" w-full" maxSuffixLenth={6} filename={filename} />
          </Box>
          {structure.type === "selection" && (
            <span ref={rangeRef} className=" flex-none  text-[12px]  text-text-common-secondary">
              {displayLineRange(structure.range)}
            </span>
          )}
        </Flex>
      </Tooltip>
    </ConfigProvider>
  );
}
