import { Dispatch, forwardRef, KeyboardEventHandler, RefObject, SetStateAction, useRef, type JSX } from "react";

import {
  MenuRenderFn,
} from "@lexical/react/LexicalTypeaheadMenuPlugin";
import { TextNode, LexicalEditor } from "lexical";
import { useCallback, useState } from "react";
import { MentionPanel } from "../MentionPanel/MentionPanel";
import { RichEditorMenuType } from "@/components/TextArea/const";
import { MentionTypeaheadOption, TypeaheadMenuOptionType, useOptions } from "../MentionPanel/useOptions";
import { MentionNodeV2Structure_File, MentionNodeV2Structure_Rule, MentionNodeV2Structure_Tree } from "shared/lib/MentionNodeV2/nodes";
import { throwNeverError } from "@/utils/throwUnknownError";
import { Popover, Portal } from "@/components/Union/chakra-ui";
import { Box, PopoverContent, PopoverTrigger, useDisclosure } from "@chakra-ui/react";
import { TypeaheadMenuContext } from "@/components/TextArea/lexical/MentionsV2Plugin";
import { MentionPanelControllerProps, MentionPanelController } from "./MentionPanelController";
import { isIdentical, useContextHeaderContext } from "./ContextHeaderContext";
import { collectMentionNodeV2 } from "./collectMentionNode";
import { kwaiPilotBridgeAPI } from "@/bridge";

export interface TypeaheadMenuState {
  anchorElementRef: Parameters<MenuRenderFn<MentionTypeaheadOption>>[0];
  itemProps: Parameters<MenuRenderFn<MentionTypeaheadOption>>[1];
  matchingString: Parameters<MenuRenderFn<MentionTypeaheadOption>>[2];
  currentMenu: TypeaheadMenuOptionType;
  setCurrentMenu: Dispatch<SetStateAction<TypeaheadMenuOptionType>>;
}

/**
 * 可以被当做 currentMenu 的类型
 */
const ALL_MENU_TYPES = [TypeaheadMenuOptionType.file, TypeaheadMenuOptionType.folder, TypeaheadMenuOptionType.rule, TypeaheadMenuOptionType.none];

export interface ContextHeaderMentionPanelProps {
  editor: LexicalEditor;
  currentMenu: TypeaheadMenuOptionType;
  setCurrentMenu: Dispatch<SetStateAction<TypeaheadMenuOptionType>>;
  onClose: () => void;
  inputRef: RefObject<HTMLInputElement>;
}

export const ContextHeaderMentionPanel = forwardRef<JSX.Element | null, ContextHeaderMentionPanelProps>(({
  currentMenu,
  setCurrentMenu,
  onClose,
  inputRef,
  editor,
}) => {
  const [queryString, setQueryString] = useState<string | null>(null);

  const { state: contextHeaderState } = useContextHeaderContext();

  const onSelectOption = useCallback(
    (
      menu: MentionTypeaheadOption,
      _nodeToReplace: TextNode | null,
    ) => {
      if (menu.type === TypeaheadMenuOptionType.heading) {
        const targetMenu = ALL_MENU_TYPES.find(type => type === menu.name);
        if (targetMenu) {
          setCurrentMenu(targetMenu);
          /* 保持 focus https://team.corp.kuaishou.com/task/B2505828 */
          inputRef.current?.focus();
        }
      }
      else if (menu.type === TypeaheadMenuOptionType.customPrompt) {
        throw new Error("not implemented");
      }
      else if (menu.type === TypeaheadMenuOptionType.file) {
        const structure = menu.structure as MentionNodeV2Structure_File;
        const existingNode = contextHeaderState.findNode(structure);

        if (existingNode) {
          // 如果有关联的 editor 节点，先删除它
          const matchedNodes = collectMentionNodeV2(editor).filter(n => isIdentical(n.__structure, existingNode.structure));
          if (matchedNodes.length > 0) {
            editor.update(() => {
              for (const matchedNode of matchedNodes) {
                matchedNode.remove();
              }
            });
          }

          // 更新 context state
          contextHeaderState.tryDeleteNode(structure);
        }
        else {
          contextHeaderState.tryInsertNode({
            structure: {
              type: "file",
              uri: structure.uri,
              relativePath: structure.relativePath,
            },
            followActiveEditor: false,
            isVirtualContext: false,
          }, {
            source: "context",
          });
        }
      }
      else if (menu.type === TypeaheadMenuOptionType.folder) {
        const structure = menu.structure as MentionNodeV2Structure_Tree;
        const existingNode = contextHeaderState.findNode(structure);

        if (existingNode) {
          contextHeaderState.tryDeleteNode(structure);
        }
        else {
          contextHeaderState.tryInsertNode({
            structure: {
              type: "tree",
              uri: structure.uri,
              relativePath: structure.relativePath,
            },
            followActiveEditor: false,
            isVirtualContext: false,
          }, {
            source: "context",
          });
        }
      }
      else if (menu.type === TypeaheadMenuOptionType.rule) {
        const structure = menu.structure as MentionNodeV2Structure_Rule;
        const existingNode = contextHeaderState.findNode(structure);

        if (existingNode) {
          contextHeaderState.tryDeleteNode(structure);
        }
        else {
          contextHeaderState.tryInsertNode({
            structure: {
              type: "rule",
              uri: structure.uri,
              relativePath: structure.relativePath,
            },
            followActiveEditor: false,
            isVirtualContext: false,
          }, {
            source: "context",
          });
        }
      }
      else if (menu.type === TypeaheadMenuOptionType.none) {
        throw new Error("not implemented");
      }
      else if (menu.type === TypeaheadMenuOptionType.header) {
        setCurrentMenu(TypeaheadMenuOptionType.none);

        /* 保持 focus https://team.corp.kuaishou.com/task/B2505828 */
        inputRef.current?.focus();
      }
      else if (menu.type === TypeaheadMenuOptionType.addRule) {
        kwaiPilotBridgeAPI.extensionComposer.$addRuleFile();
        onClose();
      }
      else {
        throwNeverError(menu.type);
      }
    },
    [setCurrentMenu, inputRef, contextHeaderState, editor, onClose],
  );

  const { options } = useOptions({
    currentMenu,
    queryString: queryString || "",
    menuType: RichEditorMenuType.SHARP_COMMAND,
  });

  const menuRenderFn: MentionPanelControllerProps<MentionTypeaheadOption>["menuRenderFn"] = useCallback(function MenuRenderComponent(
    itemProps,
    bottomElement,
  ) {
    return (
      <TypeaheadMenuContext.Provider value={{
        anchorElementRef: { current: null },
        itemProps,
        matchingString: "",
        setCurrentMenu,
        currentMenu,
      }}
      >
        <div className="typeahead-popover mentions-menu">
          <MentionPanel
            bottomElement={bottomElement}
            options={options}
            mentionType={RichEditorMenuType.SHARP_COMMAND}
            query={queryString || ""}
            selectMode="unique"
          />
        </div>
      </TypeaheadMenuContext.Provider>
    );
  }, [currentMenu, options, queryString, setCurrentMenu]);

  return (
    <MentionPanelController<MentionTypeaheadOption>
      onClose={onClose}
      onQueryChange={setQueryString}
      onSelectOption={onSelectOption}
      options={options}
      menuRenderFn={menuRenderFn}
      inputRef={inputRef}
    />
  );
});

export function MentionEditButton({ editor }: { editor: LexicalEditor }) {
  const { state: { nodes } } = useContextHeaderContext();

  const [currentMenu, setCurrentMenu] = useState<TypeaheadMenuOptionType>(TypeaheadMenuOptionType.none);

  const { isOpen, onClose, onToggle } = useDisclosure({
    onClose: useCallback(() => {
      editor.focus();
    }, [editor]),
  });

  const onPopoverBlur = useCallback<KeyboardEventHandler>((event) => {
    if (event.key === "Escape" && currentMenu !== TypeaheadMenuOptionType.none) {
      setCurrentMenu(TypeaheadMenuOptionType.none);
      event.preventDefault();
      event.stopPropagation();
    }
  }, [setCurrentMenu, currentMenu]);

  const inputRef = useRef<HTMLInputElement>(null);

  return (
    <Popover
      onOpen={() => {
        setCurrentMenu(TypeaheadMenuOptionType.none);
      }}
      placement="top-start"
      isOpen={isOpen}
      onClose={onClose}
      initialFocusRef={inputRef}
      returnFocusOnClose={false}
    >
      <PopoverTrigger>
        <Box
          onClick={onToggle}
          userSelect="none"
          alignItems="center"
          display="inline-flex"
          gap={1}
          as="button"
          height="22px"
          className="  cursor-pointer px-[6px] border border-solid border-commandCenter-inactiveBorder rounded hover:bg-toolbar-hoverBackground"
        >
          <span className=" text-[12px]  text-text-common-secondary">
            #
            {!nodes.length && <span>&nbsp;知识</span>}
          </span>
        </Box>
      </PopoverTrigger>
      <Portal>
        <PopoverContent
          onKeyDown={onPopoverBlur}
          w="auto"
          border="none"
          zIndex={20}
        >
          {isOpen && (
            <ContextHeaderMentionPanel
              editor={editor}
              currentMenu={currentMenu}
              setCurrentMenu={setCurrentMenu}
              onClose={onClose}
              inputRef={inputRef}
            />
          )}
        </PopoverContent>
      </Portal>
    </Popover>
  );
}
