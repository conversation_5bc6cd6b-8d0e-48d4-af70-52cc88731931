import { useComposerMessageConsumer } from "@/store/composerMessageConsumer";
import { useCallback, useEffect, useState } from "react";
import { ContextHeaderState } from "./ContextHeader/ContextHeaderContext";
import { firstValueFrom } from "rxjs";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { isRuleFile } from "shared/lib/util";
import { ReportOpt } from "@shared/types/logger";
import { reportUserAction } from "@/utils/weblogger";
import { useComposerState } from "../../context/ComposerStateContext";

/**
 * context 知识初始化
 *
 * * 添加followActiveEditor
 * * 同步followActiveEditor
 */
export function useContextInitiation({
  isContextConsumer,
  contextHeaderState,
}: {
  // 是否是  addComposerContext 的消费者
  isContextConsumer: boolean;
  contextHeaderState: ContextHeaderState;
}) {
  const toBeAddedComposerContext = useComposerMessageConsumer(s => s.toBeAddedComposerContext);
  const { consumeComposerContext } = useComposerMessageConsumer();
  const [editorStateInitiationDone, setEditorStateInitializationDone] = useState(false);

  const { sessionId } = useComposerState();

  const resetContext = useCallback(async () => {
    try {
      const currentFile = await firstValueFrom(kwaiPilotBridgeAPI.observableAPI.currentFileAndSelection());
      if (currentFile && !isRuleFile(currentFile.relativePath)) {
        contextHeaderState.setNodes(() => {
          const parms: ReportOpt<"input_add_context"> = {
            key: "input_add_context",
            type: "default",
            content: "currentFile",
            subType: "",
          };
          reportUserAction(parms, /* 输入框没有 chatId */"", sessionId);
          return [{
            structure: {
              type: "file",
              uri: currentFile?.uri,
              relativePath: currentFile?.relativePath,
            },
            followActiveEditor: true,
            isVirtualContext: false,
          }];
        });
      }
      else {
        contextHeaderState.setNodes([]);
      }
    }
    finally {
      setEditorStateInitializationDone(true);
    }
  }, [contextHeaderState, sessionId]);

  useEffect(() => {
    if (!isContextConsumer) {
      return;
    }
    if (!editorStateInitiationDone) {
      // 在 initiation task 中完成 否则 resetEditorState 会把这些 context 覆盖掉
      return;
    }
    // 检查是否有待添加的上下文
    if (toBeAddedComposerContext.length > 0) {
      // 获取第一个待添加的上下文
      const firstContext = toBeAddedComposerContext[0];
      // 添加到编辑器上下文
      contextHeaderState.tryInsertNode({
        structure: firstContext.message,
        followActiveEditor: false,
        isVirtualContext: false,
      }, {
        source: "shortcut",
      });
      // 从队列中移除已处理的上下文
      consumeComposerContext(firstContext);
    }
  }, [toBeAddedComposerContext, consumeComposerContext, contextHeaderState, isContextConsumer, editorStateInitiationDone]);

  return {
    resetContext,
    setEditorStateInitializationDone,
  };
}
