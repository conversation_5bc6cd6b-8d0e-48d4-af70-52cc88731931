import { InternalLocalMessage } from "shared/lib/agent";
import { useComposerState } from "../../context/ComposerStateContext";
import { UserInputTextArea } from "../UserInputTextArea/UserInputTextArea";
import { HumanMessageReadonly } from "./HumanMessageReadonly";
import { Box, BoxProps } from "@chakra-ui/react";
import { useRef, useEffect } from "react";

import { withProviders } from "@udecode/cn";
import {
  RestoreAndSendDialog,
  RestoreAndSendDialogContext,
} from "./RestoreAndSendDialog";
import {
  RestoreConfirmDialog,
  RestoreConfirmDialogContext,
} from "./RestoreConfirmDialog";
import {
  SendConfirmDialog,
  SendConfirmDialogContext,
} from "./SendConfirmDialog";

export const HumanMessage = withProviders(
  RestoreConfirmDialogContext,
  RestoreAndSendDialogContext,
  SendConfirmDialogContext,
)(function HumanMessage({
  data,
  ...rest
}: { data: InternalLocalMessage } & BoxProps) {
  const {
    editingMessageTs,
  } = useComposerState();

  const isEditing = editingMessageTs === data.ts;
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isEditing) {
      setTimeout(() => {
        const container = containerRef.current;
        if (container) {
          (container as any).scrollIntoViewIfNeeded();
        }
      }, 0);
    }
  }, [isEditing]);

  return (
    <>
      <Box {...rest} role="group" ref={containerRef}>
        {isEditing
          ? (
              <UserInputTextArea
                localMessage={data}
                hiddenApplyStatus
                role="conversation"
              />
            )
          : (
              <HumanMessageReadonly data={data} />
            )}
      </Box>

      <RestoreConfirmDialog />
      <RestoreAndSendDialog />
      <SendConfirmDialog />
    </>
  );
});
