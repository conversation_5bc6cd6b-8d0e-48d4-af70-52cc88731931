// NOTE: ide 组件入口，不影响插件逻辑
import { kwaiPilotBridgeAPI } from "@/bridge-export";
import { VSCodeNativeBridge } from "@/bridge-export/kwaipilotBridge";
import "./mount-export.css";
import { initHighlighterInstance } from "./utils/highlighter";
/**
 * mountApp函数的选项接口
 */
interface MountAppOptions {
  customTheme?: any;
}

/**
 * 挂载应用到指定DOM元素
 *
 * @param rootElement - 要挂载应用的DOM元素
 * @param bridge - 桥接对象实现
 * @param options - 配置选项，可自定义主题等
 * @returns 包含重新渲染和销毁方法的对象
 */
const mountApp = async (
  rootElement: HTMLElement,
  bridge: VSCodeNativeBridge,
  options: MountAppOptions,
) => {
  if (bridge) {
    try {
      await initHighlighterInstance();
    }
    catch (error) {
      console.error("mountApp: 初始化高亮器失败", error);
    }
    try {
      kwaiPilotBridgeAPI.setBridge(bridge);
      console.log("mountApp: bridge实现已成功注入");
    }
    catch (error) {
      console.error("mountApp: bridge实现注入失败", error);
    }
  }
  else {
    console.error("mountApp: 未提供bridge实现，API调用会失败");
  }

  import("./mountApp-export").then(({ default: mountApp }) => {
    mountApp(rootElement, options);
  });
};

export { mountApp };
