import { createMemoryRouter } from "react-router-dom";
import { Home } from "@/pages/home";
import { Chat } from "@/pages/chat";
import { InlineChat } from "@/pages/inline-chat";
import History from "@/pages/history";
import { PageComposer } from "@/pages/composer";
import { PageComposerV2 } from "@/pages/composer-v2";
import { getLocalStorageValue } from "@/utils/localStorage";

// NOTE: 不允许默认打开的窗口是InlineChat
const initialEntrie
  = getLocalStorageValue("activePath") === "/inline-chat"
    ? "/"
    : getLocalStorageValue("activePath");

export const router = createMemoryRouter(
  [
    {
      path: "/",
      element: <Home />,
    },
    {
      path: "chat",
      element: <Chat />,
    },
    {
      path: "inline-chat",
      element: <InlineChat />,
    },
    {
      path: "history",
      element: <History />,
    },
    {
      path: "composer",
      element: <PageComposer />,
    },
    {
      path: "composer-v2",
      element: <PageComposerV2 />,
    },
  ],
  { initialEntries: [initialEntrie ?? "/"] },
);
