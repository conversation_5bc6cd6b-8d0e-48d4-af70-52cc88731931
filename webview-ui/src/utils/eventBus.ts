import { RecordStoreVendor } from "@/store/record";
import { EventEmitter } from "@infra-node/tee";
import { CodeActionParams, DisableRichEditorMenu } from "@shared/types";
import { useEffect } from "react";
import { InternalLocalMessage_Human } from "shared/lib/agent";

export type EventBusEvents = {
  "inlineChat": () => void;
  "home": () => void;
  "chat": () => void;
  "actionResult": ({
    data,
    id,
  }: {
    data: string;
    id: string;
    vendor: RecordStoreVendor;
  }) => void;
  "startInlineChat": ({
    chatId,
    question,
    sessionId,
  }: {
    chatId: string;
    question: string;
    sessionId: string;
  }) => void;
  "actionForCode": (params: CodeActionParams) => void;
  // repo chat
  "pushRichEditorPanelDisableMenu": (data: DisableRichEditorMenu) => void;
  "inlineChat:message": (data: string) => void;
  "inlineChat:messageDone": (data?: string) => void;
  "composer:checkPointConfirm": (editorState?: string) => void;
  "composer:onResetCheckpoint": (payload: {
    humanMessage: InternalLocalMessage_Human;
  }) => void;
};
const eventBus = new EventEmitter<EventBusEvents>();
export default eventBus;

export function useEventBusListener<T extends keyof EventBusEvents>(eventName: T, handler?: null | undefined | EventBusEvents[T]) {
  useEffect(() => {
    if (!handler) {
      return;
    }
    eventBus.on(eventName, handler);
    return () => {
      eventBus.off(eventName, handler);
    };
  }, [eventName, handler]);
}
