import { Weblog } from "@ks/weblogger/lib/log.hybrid";
import { getActiveSessionId } from "./getActiveSessionId";
import Radar from "@ks-radar/radar";
import { chatId } from "./chatId";
import baseInfoManager from "./baseInfo";
import { logger } from "./logger";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { ReportKeys, ReportOpt } from "@shared/types/logger";
import { LoggerCustomEventStructure } from "shared/lib/misc/logger";

export let weblog: Weblog<object> | null = null;
let radar: Radar | null = null;

export const initWeblogger = () => {
  weblog = new Weblog(
    // 初始化参数配置
    {
      env: import.meta.env.DEV ? "logger" : "production", // 生产环境埋点
      disableCompress: true, // 不压缩
    },
    // 埋点基础信息配置
    {
      product_name: "data_kwaipilot", // 当前埋点产品的 product_name
    },
  );
  // 添加插件版本
  weblog.updateCommonPackage({});

  radar = new Radar({
    weblog,
    projectId: "8ce1e9a025", // 雷达项目ID
    // 其他参数详见：https://ksurl.cn/8EUcHTfF
  });
};

export const updateCommonPackage = (baseOption: any) => {
  try {
    weblog?.updateCommonPackage(baseOption);
  }
  catch (e) {
    console.error(e);
  }
};

export const collectPV = (page: string, params?: any) => {
  try {
    weblog?.collect("PV", {
      type: "enter",
      page: page,
      params: params,
    });
  }
  catch (e) {
    console.error(e);
  }
};

export const collectClick = (action: string, params?: any) => {
  try {
    weblog?.collect("CLICK", {
      action: action,
      params: params,
    });
  }
  catch (e) {
    console.error(e);
  }
};

export const reportUserAction = (
  opt?: ReportOpt<keyof ReportKeys>,
  selfId?: string,
  sessionId?: string,
) => {
  if (!opt) return;
  const { key, type, content, subType, applyId } = opt;
  const cId = selfId ?? chatId.getChatId();
  logger.info("report user action", "webview:weblogger", {
    value: { key, type },
  });
  weblog?.sendImmediately("CUSTOM", {
    key: key,
    value: {
      // vscode对话是否有id概念，如果有，应该有个方法获取
      conversationId: sessionId ?? getActiveSessionId() ?? "-1",
      time: new Date().valueOf(),
      device: baseInfoManager.ide,
      operator: "",
      type,
      content: content || "",
      subType: subType || "",
      chatId: cId,
      applyId: applyId || "",
      pluginVersion: baseInfoManager.pluginVersion,
      ideVersion: baseInfoManager.ideVersion,
      platform: baseInfoManager.platform,
      release: baseInfoManager.release,
      arch: baseInfoManager.arch,
      machine: baseInfoManager.machine,
      hostname: baseInfoManager.hostname,
      isInWorkspace: baseInfoManager.isInWorkspace,
    } satisfies LoggerCustomEventStructure,
  });
};
let hasReport = false;
export const reportFMP = () => {
  if (hasReport) return;
  hasReport = true;
  const now = performance.now();
  logger.info(`report FMP: ${now}`, "weblogger.ts", { value: { now } });
  radar?.fmp();
};

kwaiPilotBridgeAPI.logger.onReportUserAction(
  (params: ReportOpt<keyof ReportKeys>) => {
    reportUserAction(params);
  },
);
