import { getLocalStorageValue, LocalStorageKey } from "./localStorage";
import { getActiveSession } from "./sessionUtils";

export const getActiveSessionId = () => {
  const inlinSessionId = getLocalStorageValue("activeSessionId");
  const chatSession = getActiveSession();

  return inlinSessionId ?? chatSession;
};

export const getActiveComposerSessionId = () => {
  return getLocalStorageValue(LocalStorageKey.activeComposerSessionId);
};
