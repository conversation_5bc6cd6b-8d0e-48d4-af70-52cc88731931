import { CustomScrollBar } from "@/components/CustomScrollbar";
import { HistoryBar } from "@/components/HistoryBar";
import useNavigateWithCache from "@/hooks/useNavigateWithCach";
import eventBus from "@/utils/eventBus";
import { useEffect } from "react";
import HistoryContent from "./Content";
import ClearHistory from "./ClearHistory";

const History = () => {
  const navigate = useNavigateWithCache();
  useEffect(() => {
    eventBus.on("home", () => {
      navigate("/");
    });
    eventBus.on("chat", () => {
      navigate("/chat");
    });
  }, [navigate]);
  return (
    <div className="flex flex-col  h-full min-w-[299px] overflow-scroll overflow-y-hidden bg-sideBar-background">
      <HistoryBar current="history" action={<ClearHistory />} />
      <div className="flex flex-col justify-between w-full h-full overflow-hidden">
        <CustomScrollBar className="overflow-y-scroll overscroll-none px-[12px] pt-3">
          <HistoryContent />
        </CustomScrollBar>
      </div>
    </div>
  );
};

export default History;
