import { TextArea } from "@/components/TextArea";
import useNavigateWithCache from "@/hooks/useNavigateWithCach";
import eventBus from "@/utils/eventBus";
import { useCallback, useEffect, useRef } from "react";
import Logo from "./Logo";
import { HistoryBar } from "@/components/HistoryBar";
import { RecordProvider } from "@/store/record";
import { NotLoggedHint } from "@/components/NotLoggedHint";
import { useUserStore } from "@/store/user";
import { useIdeEnv } from "@/hooks/useIdeEnv";

export const Home = () => {
  const navigate = useNavigateWithCache();
  const [, isKwaiPilotIDE] = useIdeEnv();

  const userInfo = useUserStore(state => state.userInfo);
  const goChat = useCallback(() => {
    navigate("/chat");
  }, [navigate]);
  useEffect(() => {
    eventBus.on("chat", goChat);
    return () => {
      eventBus.off("chat", goChat);
    };
  }, [goChat]);

  const signRef = useRef<any>(null);
  const setSignRef = useCallback((ref: any) => {
    signRef.current = ref;
  }, []);

  if (isKwaiPilotIDE)
    return (
      <div className="flex h-screen w-screen min-w-[275px] flex-col justify-center items-center bg-sideBar-background">
        <HistoryBar />

        <div className="w-full flex h-full px-[16px] flex-col justify-center items-center overflow-auto">
          {userInfo
            ? (
                <div className="h-[242px] max-h-[242px] pt-[12px] w-full mt-[40px] flex flex-col items-center relative">
                  <div className="w-[calc(100%-4px)]">
                    <RecordProvider vendor="chat">
                      <TextArea
                        editorClassName="h-[80px]"
                        forwardedRefInsertCommandSignRef={setSignRef}
                      />
                    </RecordProvider>
                  </div>
                </div>
              )
            : (
                <div className=" mt-9 bg-input-bg-input-fill rounded-lg border-2 border-solid border-[#33455A]">
                  <NotLoggedHint pt={2} px={4} />
                </div>
              )}
        </div>
      </div>
    );

  return (
    <div className="flex h-screen w-screen min-w-[275px] flex-col justify-center items-center bg-sideBar-background">
      <HistoryBar />

      <div className="w-full flex h-full px-[16px] flex-col justify-start items-center overflow-auto">
        <div className="w-full flex items-end justify-center relative mt-[15vh]">
          <Logo></Logo>
        </div>
        {userInfo
          ? (
              <div className="h-[242px] max-h-[242px] pt-[12px] w-full mt-[40px] flex flex-col items-center relative">
                <div className="w-[calc(100%-4px)]">
                  <RecordProvider vendor="chat">
                    <TextArea
                      editorClassName="h-[80px]"
                      forwardedRefInsertCommandSignRef={setSignRef}
                    />
                  </RecordProvider>
                </div>
              </div>
            )
          : (
              <div className=" mt-9 bg-input-bg-input-fill rounded-lg border-2 border-solid border-[#33455A]">
                <NotLoggedHint pt={2} px={4} />
              </div>
            )}
      </div>
    </div>
  );
};
