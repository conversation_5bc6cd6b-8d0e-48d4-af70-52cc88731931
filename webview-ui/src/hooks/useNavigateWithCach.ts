import { setLocalStorageValue } from "@/utils/localStorage";
import { logger } from "@/utils/logger";
import { NavigateOptions, useNavigate } from "react-router-dom";

const useNavigateWithCache = () => {
  const n = useNavigate();

  const navigate = (
    to: "/chat" | "/" | "/inline-chat" | "/composer" | "/composer-v2" | number,
    options?: NavigateOptions,
  ) => {
    logger.info("navigate to", "navigate", { value: to });
    setLocalStorageValue("activePath", to.toString());
    typeof to === "number" ? n(to) : n(to, options);
  };

  return navigate;
};

export default useNavigateWithCache;
