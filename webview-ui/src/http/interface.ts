export interface CodeSearchCheckoutRequest {
  repoName: string;
  commit: string;
  branch: string;
  username: string;
}

export interface GeneratePromptFile {
  code: string;
  language: string;
  name: string;
}

export interface CodeSearchGeneratePromptRequest {
  codebaseSearch: boolean;
  commit: string;
  files: GeneratePromptFile[];
  query: string;
  repoName: string;
  searchTargetDirs: string[];
  username: string;
  instantApplyMode: boolean;
  rules?: string[];
}

export interface LocalCodeSearchGeneratePromptRequest {
  question: string;
  rules?: string[];
  codeContextList: {
    _sub_node: boolean;
    code_content: string;
    metadata: {
      callees: string[];
      callers: string[];
      code_type: string;
      file_path: string;
      language: string;
      name: string;
      signature: string;
    };
    node_id: string;
    sub_node_id: number;
  }[];
}

export interface HttpClientResponse<T> {
  data: T;
  host: string;
  port: number;
  message: string;
  status: number;
  timestamp: string;
  traceId: string;
}

export interface PlatformConfigResponse {
  codeScreenShotConfig: {
    shotIntervalSeconds: number;
    shotSize: number;
    uploadCodeScreenShot: boolean;
  };
  promptConfigs: {
    key: string;
    name: string;
    template: string;
  }[];
  showTips: boolean;
  supportedPlatform: string[];
  supportedQaPlatform: {
    desc: string;
    multiValueList: string[];
    name: string;
    oncallTipMessage: string;
    value: string;
  }[];
  codePredictionConfig: {
    editHistorySize: number;
    editValidIntervalMs: number;
  };
}

export interface GetCodeSearchPromptResponse {
  prompt: string;
  list: [];
}

export interface GetCodeSearchCheckoutResponse {
  status: number;
}
export interface FetchEventSourceInit extends RequestInit {
  headers?: Record<string, string>;
  onopen?: (response: Response) => Promise<void>;
  onmessage?: (ev: EventSourceMessage) => void;
  onclose?: () => void;
  onerror?: (err: any) => number | null | undefined | void;
  openWhenHidden?: boolean;
  fetch?: typeof fetch;
}

export interface EventSourceMessage {
  id: string;
  event: string;
  data: string;
  retry?: number;
}

export interface GetPredictionResponse {
  diffBlocks: Prediction[];
}

export interface Prediction {
  diffBlockId: string;
  diffDeltas: PredictionDiffDelta[];
  sourceBlockContent: string;
  sourceStartOffset: number;
  sourceEndOffset: number;
  targetBlockContent: string;
  viewPosition: "after" | "inline";
}

export interface PredictionDiffDelta {
  // targetContent: {
  //   startOffset: number;
  //   endOffset: number;
  //   content: string;
  // };
  // SourceContent: {
  //   startOffset: number;
  //   endOffset: number;
  //   content: string;
  // }
  startOffset: number;
  endOffset: number;
  sourceContent: string;
  targetContent: string;
}

export interface WidgetConfig {
  // 活动名称, 如 "2024调查问卷"
  name: string;
  // 活动开始时间, 如：2024-05-01
  startTime: string;
  // 活动结束时间, 如：2024-05-10
  endTime: string;
  // 下发平台: vscode idea xcode
  platform: string[]; // ["vscode", "idea", "xcode"];
  // 背景图
  background: {
    light: string; // "linear-gradient(92deg, #fff 6.43%, #d7f0ff 92.4%)";
    dark: string; // "linear-gradient(92deg, #000 6.49%, #002e92 95.65%)";
  };
  // 一层背景图，居右展示
  decorate: {
    light: string; // "url('https://ali.a.yximgs.com/kos/nlav12119/woNnsfyP_2024-08-01-19-05-35.png')";
    dark: string; // "url('https://ali.a.yximgs.com/kos/nlav12119/nmzjEnzn_2024-08-01-19-06-57.png')";
  };
  // 左侧图标
  icon: string; // "https://ali.a.yximgs.com/kos/nlav12119/DcVCOXRX_2024-08-23-18-20-09.png";
  // 活动标题（主要文本）20字以内
  title: string; // "智能体大赛开始啦";
  // 文字链接
  link: {
    // 链接展示文本
    text: string; // "立即参与";
    // 跳转链接,
    url: string; // "https://kwaipilot.corp.kuaishou.com/agents";
  };
}

interface CodeFile {
  code: string;
  language: string;
  name: string;
}
export interface TreeNode {
  name: string;
  children: TreeNode[];
  type: 0 | 1;
}
export interface ComposerPromptBody {
  codeFiles: CodeFile[];
  projectName: string;
  round: number;
  projectTree: TreeNode[];
  question: string;
}
