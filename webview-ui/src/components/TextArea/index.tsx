import { use<PERSON><PERSON>back, useEffect, useState, useMemo, useRef } from "react";
import clsx from "clsx";

import { getRecordStoreByVendor, ReocrdStoreVendorEnum, useRecordStore } from "@/store/record";
import { useUserStore } from "@/store/user";
import { MessageType } from "@/utils/const";
import {
  genNewActiveSession,
  generateCustomUUID,
  postMessageUtil,
} from "@/utils/sessionUtils";
import { collectClick, reportUserAction } from "@/utils/weblogger";
import { ReportOpt } from "@shared/types/logger";
import { usePromptTemplate } from "@/store/promptTemplate";
import {
  CommandType,
  SlashCommand,
  // RichEditorBoxPanelData,
} from "@shared/types";
import { chatId } from "@/utils/chatId";
import { RichEditor } from "./RichEditor";
import { $createParagraphNode, $getRoot, LexicalEditor, SerializedEditorState, SerializedLexicalNode } from "lexical";
import {
  preparePromptGenerationQuery,
  getSlashCommandReportType,
  removeCustomHtmlBlock,
} from "./utils";
import { isMentionNode, MentionNode } from "./lexical/CommandNode";
import { logger } from "@/utils/logger";
import { FileCard } from "@/components/FileCard";
import { CustomScrollBar } from "@/components/CustomScrollbar";
import { ModelArea } from "./components/ModelArea";
import css from "@/components/TextArea/index.module.less";
import { DEFAULT_MODEL_TYPE } from "@/constant";
import { ICachedMessageQuestionV2, QAItem } from "@shared/types/chatHistory";
import { getCurrentSessionTimeString } from "@/utils/utils";
import { Enable, Resizable } from "re-resizable";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { httpClient } from "@/http";
import repoChatService from "@/services/repo-chat";
import { fetchSummaryConversation } from "@/http/api/summaryConversation";
import ApplyStatus from "./components/ApplyStatus";
import { ComposerPromptBody } from "@/http/interface";
import useNavigateWithCache from "@/hooks/useNavigateWithCach";
import { useHistoryStore } from "@/store/history";
import { collectSharpCommandLog, haveSharpCommand } from "./lexical/SharpPlugin";
import { transformToPlainTextForHumanReading } from "./lexical/editorState";
import { findFirst } from "./lexical/traversal";
import { produce } from "immer";
import eventBus from "@/utils/eventBus";
import { SerializedMentionNode } from "shared/lib/MentionNode";
import { collectContextLog } from "@/logics/composer/components/UserInputTextArea/useSubmit";
import { getRootContainer } from "@/utils/dom";

export interface UploadFile {
  biz?: string;
  filename: string;
  id?: number;
  uid: string;
  path?: string;
  url?: string;
  type?: string;
  size: number;
  username?: string;
  progress?: number;
  status?: "uploading" | "done" | "error";
  [key: string]: any;
}
export interface DocListType {
  id: number;
  name: string;
  urls?: string[];
}

export interface CommandPluginRef {
  close: () => void;
  open: () => void;
  getShown: () => boolean;
}

interface IProps {
  editorClassName?: string;
  wrapperClassName?: string;
  forwardedRefInsertCommandSignRef?: (
    ref: React.MutableRefObject<
      | {
        insertSign: (sign: string) => void;
      }
      | undefined
    >
  ) => void;
  enable?: Enable | false;
  customOptions?: {
    autoInsertCurrentFileCommand?: boolean;
    slashCommandEnabled?: boolean;
    sharpCommandEnabled?: boolean;
    uploadFileEnabled?: boolean;
  };
  placeholder?: string;
}

export const TextArea: React.FC<IProps> = (props: IProps) => {
  const {
    editorClassName,
    forwardedRefInsertCommandSignRef,
    wrapperClassName,
    customOptions,
    placeholder,
  } = props;
  const chatModel = useRecordStore(state => state.chatModel);
  const loadingStatu = useRecordStore(state => state.loadingStatu);
  const activeSession = useRecordStore(state => state.activeSession);
  const setActiveSession = useRecordStore(state => state.setActiveSession);
  const useSearch = useRecordStore(state => state.useSearch);
  const docId = useRecordStore(state => state.docId);
  const setSessionHistory = useRecordStore(state => state.setSessionHistory);
  const abortCurrentChat = useRecordStore(state => state.abortCurrentChat);
  const sessionHistory = useRecordStore(state => state.sessionHistory);
  const summaryConversation = useRecordStore(
    state => state.summaryConversation,
  );
  const clearSuggestQuestion = useRecordStore(
    state => state.clearSuggestQuestion,
  );
  const userInfo = useUserStore(state => state.userInfo);
  const updateHistoryList = useHistoryStore(state => state.updateHistoryList);
  const navigate = useNavigateWithCache();
  const isComposer = useRecordStore(state =>
    Boolean(state.vendor === ReocrdStoreVendorEnum.composer),
  );
  const setLoadingStatu = useRecordStore(state => state.setLoadingStatu);

  const optRef = useRef<HTMLDivElement>(null);
  const sharpPluginRef = useRef<CommandPluginRef>();
  const slashPluginRef = useRef<CommandPluginRef>();
  const fileList = useRecordStore(state => state.fileList);

  const activeSessionId = activeSession;
  const removeFile = useRecordStore(state => state.removeFile);
  const repoPathAndWorkspacePathRef = useRef<{
    repoPath?: string;
    workspacePath?: string;
  }>({});

  const editorRef = useRef<LexicalEditor>(null);

  const clearRef = useRef<{
    clear: () => void;
  }>();
  const insertSignRef = useRef<{
    insertSign: (sign: string) => void;
  }>();
  const [richEditorState, setRichEditorState]
    = useState<SerializedEditorState<SerializedLexicalNode>>();
  const changeEditorState = useCallback(
    (state: SerializedEditorState<SerializedLexicalNode>) => {
      setRichEditorState(state);
    },
    [setRichEditorState],
  );

  const promptTemplate = usePromptTemplate();

  const [focused, setFocused] = useState(false);
  /** 进入子菜单要标识进入的是哪个子菜单 */
  const panelRef = useRef<HTMLDivElement>(null);

  /** resize时设置最小高度 */
  const fileRef = useRef<HTMLDivElement>(null);

  const disabled = useMemo(() => {
    try {
      const children = richEditorState?.root?.children;
      let sub = "";
      children?.forEach((child: any) => {
        const subChildren = child?.children as SerializedMentionNode[];
        subChildren?.forEach((i) => {
          sub += i.text || "";
        });
      });
      return !sub.trim();
    }
    catch (error) {
      return true;
    }
  }, [richEditorState]);

  const getWorkspacePathAndRepoPath = useCallback(async () => {
    const data = await repoChatService.getWorkspacePathAndRepoPath();
    repoPathAndWorkspacePathRef.current = data;
  }, []);

  useEffect(() => {
    eventBus.on("composer:checkPointConfirm", (editorState) => {
      if (editorState) {
        editorRef.current?.setEditorState(editorRef.current.parseEditorState(editorState));
      }
    });
  }, []);

  /** 转发插入内容给外部 */
  useEffect(() => {
    forwardedRefInsertCommandSignRef?.(insertSignRef);
  }, [forwardedRefInsertCommandSignRef]);

  /** 获取工作区和代码仓库路径 */
  useEffect(() => {
    getWorkspacePathAndRepoPath();
  }, [getWorkspacePathAndRepoPath]);

  const onSubmit = useCallback(async () => {
    collectClick("VS_SUBMIT_BUTTON");
    const params: ReportOpt<"chat"> = {
      key: "chat",
      type: "new-chat",
    };

    const startTime = Date.now();

    if (disabled) {
      return;
    }
    clearSuggestQuestion();

    const editorState = richEditorState;
    if (!editorState || !editorRef.current) {
      kwaiPilotBridgeAPI.showToast({
        level: "error",
        message: "编辑器未初始化, 请重试",
      });
      return;
    }
    if (loadingStatu && loadingStatu.status === "loading") {
      kwaiPilotBridgeAPI.showToast({
        level: "error",
        message: "正在生成回答，请稍后尝试",
      });
      return;
    }
    clearRef.current?.clear();
    let sessionId = activeSessionId;
    const uniqueId
      = Date.now().toString(36) + Math.random().toString(36).substr(2);
    chatId.updateChatId(uniqueId);

    reportUserAction(params);
    const refFiles = fileList.map(i => Number(i.id));

    const extraInfo = {
      startLine: 0,
      endLine: 0,
      language: "",
      filename: "",
      code: "",
    };

    // 在修改编辑器状态前先保存原始状态，确保包含所有sharp命令。slash命令会更新编辑器状态
    const originalEditorState = editorRef.current.getEditorState().toJSON();

    const slashCommand = findFirst(editorState, (node): node is SerializedMentionNode => isMentionNode(node) && node.commandType === CommandType.SLASH);

    const questionForHumanReading = transformToPlainTextForHumanReading(editorState);

    if (slashCommand) {
      // 如果 editorState 中有 slash 命令，则返回一个简化的 editorState
      editorRef.current.update(() => {
        const root = $getRoot();
        root.clear();
        const p = $createParagraphNode();
        root.append(p);
        const commandNode = MentionNode.importJSON(slashCommand);
        console.log(commandNode);
      }, { discrete: true });
    }
    // 把提问和回答消息放进数组用于展示
    const newCacheMessage: QAItem = {
      id: uniqueId,
      Q: {
        isSelf: true,
        id: uniqueId,
        question: questionForHumanReading,
        reply: "",
        fileList,
        modelType: chatModel.modelType || DEFAULT_MODEL_TYPE,
        v2: true,
        plainText: questionForHumanReading,
        editorState: originalEditorState, // 使用保存的原始状态，而不是可能被修改后的状态
      } satisfies ICachedMessageQuestionV2,
      A: [
        {
          isSelf: false,
          modelType: chatModel.modelType ?? DEFAULT_MODEL_TYPE,
          id: uniqueId,
          question: questionForHumanReading,
          reply: "",
          answerId: generateCustomUUID(),
          indexing: false,
        },
      ],
    };

    const promptGenerationQuery = preparePromptGenerationQuery(
      editorState,
      {
        promptConfig: promptTemplate.promptTemplate || [],
        language: extraInfo.language,
        code: extraInfo.code,
      },
      repoPathAndWorkspacePathRef.current,
    );

    const parsedRules = await kwaiPilotBridgeAPI.extensionToLoacl.$parseRules(promptGenerationQuery.rules);
    let formatQuestion = promptGenerationQuery.query;

    if (slashCommand) {
      if (slashCommand.key === SlashCommand.CLEAR_CONVERSATION) {
        abortCurrentChat();
        if (sessionHistory) {
          const newClearContextIndex = [
            ...new Set([
              ...sessionHistory.clearContextIndex,
              sessionHistory.cachedMessages.length - 1,
            ]),
          ];
          setSessionHistory({
            ...sessionHistory,
            clearContextIndex: newClearContextIndex,
          });
          kwaiPilotBridgeAPI.updateSessionInfo({
            sessionId: sessionHistory.sessionId,
            expiredIndex: sessionHistory.expiredIndex,
            clearContextIndex: newClearContextIndex,
          });
        }
        return;
      }
      else {
        const { code, endLine, startLine, filename, language }
          = await kwaiPilotBridgeAPI.getSelectionContent();
        extraInfo.code = code;
        extraInfo.endLine = endLine;
        extraInfo.startLine = startLine;
        extraInfo.filename = filename;
        extraInfo.language = language;
        newCacheMessage.Q = { ...newCacheMessage.Q, ...extraInfo };
        /** 没取到代码直接退出 */
        if (!code) {
          kwaiPilotBridgeAPI.showToast({
            level: "error",
            message: "请选中代码块",
          });
          return;
        }
        const useInstantApplyMode
          = slashCommand.key !== SlashCommand.UNIT_TEST
          && slashCommand.key !== SlashCommand.CODE_EXPLAIN;
        const prompt = await httpClient.getCodeSearchPrompt({
          files: [
            {
              code,
              language,
              name: filename,
            },
          ],
          searchTargetDirs: [],
          query: promptGenerationQuery.query,
          codebaseSearch: false,
          commit: await repoChatService.getCommit(),
          repoName: repoChatService.repoName,
          username: userInfo?.name ?? "",
          instantApplyMode: useInstantApplyMode,
          rules: parsedRules,
        });
        if (!prompt) {
          return;
        }

        /** codeSearch相关引用 */
        newCacheMessage.A[0].codeSearchList = prompt.list;
        formatQuestion = prompt.prompt;
      }
    }
    // 立即更新会话历史以展示问题
    const newSessionTime = getCurrentSessionTimeString();
    if (sessionHistory) {
      sessionId = sessionHistory.sessionId;
      setSessionHistory({
        ...sessionHistory,
        cachedMessages: [
          ...(sessionHistory?.cachedMessages ?? []),
          newCacheMessage,
        ],
        sessionTime: newSessionTime,
      });
    }
    else {
      // 新建对话
      sessionId = generateCustomUUID();
      const session = genNewActiveSession(sessionId);
      // 切换至该对话
      setActiveSession({
        value: session,
        updateLocalStorage: !!userInfo,
        updateHistory: false, // 不需要从 sqlite拉取历史记录
      });
      // 初始化新对话历史消息
      const newSession = {
        sessionId: session,
        sessionName: summaryConversation ? "" : removeCustomHtmlBlock(questionForHumanReading),
        sessionTime: newSessionTime,
        cachedMessages: [newCacheMessage],
        expiredIndex: [],
        clearContextIndex: [],
        isComposer,
      };
      if (summaryConversation) {
        fetchSummaryConversation(questionForHumanReading, (chunk) => {
          updateHistoryList(sessionId, chunk, false);
        });
      }

      setSessionHistory(newSession);
      if (userInfo) {
        kwaiPilotBridgeAPI.addSession(newSession);
      }
    }
    if (isComposer) {
      navigate("/composer");
    }
    else {
      navigate("/chat");
    }

    // 立即设置loading状态
    setLoadingStatu(uniqueId, "loading");
    // 立即滚动到底部
    const dialogContainer = getRootContainer()?.querySelector(".chat-dialog-container");
    if (dialogContainer) {
      setTimeout(() => {
        dialogContainer.scrollTo({
          top: 0,
          behavior: "smooth",
        });
      }, 100);
    }

    let composerBody: ComposerPromptBody | undefined = undefined;

    if (isComposer) {
      const { list: openFiles } = await kwaiPilotBridgeAPI.getOpenTabFiles();
      const projectTree = await repoChatService.getWorkspaceTree();
      composerBody = {
        question: promptGenerationQuery.query,
        codeFiles: await repoChatService.getSelectFileCode(openFiles),
        projectName: repoChatService.repoName,
        round: sessionHistory?.cachedMessages.length ?? 1,
        projectTree: projectTree,
      };
    }
    const haveSharp = haveSharpCommand(editorState);
    if (haveSharp) {
      /**
       * hack: 修复一个添加历史记录时选中项可能是空的情况
       */

      repoChatService.updateCodeSearchSelectHistory({
        file: promptGenerationQuery.files.filter(i => i.trim() !== ""),
        dir: promptGenerationQuery.dirs.filter(i => i.trim() !== ""),
      });

      logger.info("get code search select item", "code-search", {
        value: {
          files: promptGenerationQuery.files,
          dirs: promptGenerationQuery.dirs,
          query: promptGenerationQuery.query,
          codebase: promptGenerationQuery.codebase,
        },
      });
      let feeback = false;
      const useLocalCodeSearch = promptGenerationQuery.dirs.length !== 0 || !!promptGenerationQuery.codebase;
      if (useLocalCodeSearch) {
        const chatHistory: {
          role: "user" | "assistant";
          content: string;
        }[] = [];
        sessionHistory?.cachedMessages.forEach((i) => {
          chatHistory.push({
            role: "user",
            content: i.Q.question,
          });
          chatHistory.push({
            role: "assistant",
            content: i.A[0].reply ?? "",
          });
        });
        const { status, data, message }
          = await kwaiPilotBridgeAPI.agent.searchSearch({
            query: promptGenerationQuery.query,
            chatHistory: chatHistory ?? [],
            targetDirectory: promptGenerationQuery.dirs,
            topK: 10,
          });
        if (status === "failed") {
          logger.error("search search failed", "code-search", {
            value: {
              message,
            },
          });
          feeback = true;
        }
        else if (!data) {
          feeback = true;
        }
        else {
          const { code_context_list: codeContentList } = data;

          const prompt = await httpClient.getLocalCodeSearchPrompt({
            question: promptGenerationQuery.query,
            codeContextList: codeContentList,
            rules: parsedRules,
          });
          if (!prompt) {
            feeback = true;
          }
          else {
            formatQuestion = prompt;
          }
        }
      }

      if (feeback || !useLocalCodeSearch) {
        const files = await repoChatService.getSelectFileCode(promptGenerationQuery.files);
        const useInstantApplyMode = promptGenerationQuery.dirs.length === 0 && !promptGenerationQuery.codebase;
        if (composerBody) {
          composerBody.codeFiles = files;
        }
        else {
          const prompt = await httpClient.getCodeSearchPrompt({
            files,
            searchTargetDirs: promptGenerationQuery.dirs,
            query: promptGenerationQuery.query,
            codebaseSearch: promptGenerationQuery.codebase ? true : false,
            commit: await repoChatService.getCommit(),
            repoName: repoChatService.repoName,
            username: userInfo?.name ?? "",
            instantApplyMode: useInstantApplyMode,
            rules: parsedRules,
          });
          if (!prompt) {
            return;
          }
          /** codeSearch相关引用 */
          const ns = getRecordStoreByVendor(isComposer ? "composer" : "chat").getState().sessionHistory;
          if (ns) {
            setSessionHistory(
              produce(ns, (draft) => {
                if (!draft) {
                  return;
                }
                draft.cachedMessages[draft.cachedMessages.length - 1].A[0].codeSearchList = prompt?.list;
              }),
            );
          }
          formatQuestion = prompt.prompt;
        }
      }
    }

    if (composerBody) {
      const composerPrompt = await httpClient.getComposerPrompt(composerBody);
      // 拒绝所有文件
      kwaiPilotBridgeAPI.editor.rejectAllFileDiff();
      if (!composerPrompt) {
        formatQuestion = promptGenerationQuery.query;
      }
      else {
        formatQuestion = composerPrompt;
      }
    }
    const ns = getRecordStoreByVendor(isComposer ? "composer" : "chat").getState().sessionHistory;
    if (ns) {
      setSessionHistory(
        produce(ns, (draft) => {
          if (!draft) {
            return;
          }
          draft.cachedMessages[draft.cachedMessages.length - 1].Q.formatQuestion = formatQuestion;
          draft.cachedMessages[draft.cachedMessages.length - 1].A[0].formatQuestion = formatQuestion;
        }),
      );
    }

    if (!userInfo) {
      return;
    }
    const sharpCommandLog = collectSharpCommandLog(editorState);

    /** 上报埋点 /指令 #知识库 */
    if (slashCommand) {
      const param: ReportOpt<"shortcutInstruction"> = {
        key: "shortcutInstruction",
        type: getSlashCommandReportType(slashCommand.key),
      };
      reportUserAction(param);
    }
    else if (sharpCommandLog) {
      const param: ReportOpt<"codeSearch"> = {
        key: "codeSearch",
        type: sharpCommandLog,
      };
      reportUserAction(param);
    }

    const contextLog = collectContextLog(editorState, []);
    if (contextLog) {
      const param: ReportOpt<"input_context_send"> = {
        key: "input_context_send",
        type: contextLog,
        content: "common_chat",
      };
      reportUserAction(param);
    }

    postMessageUtil({
      rules: parsedRules,
      content: formatQuestion,
      type: MessageType.SEND_MESSAGE,
      cachedMessages: sessionHistory?.cachedMessages ?? [],
      chatType: "intelligentChat",
      sessionId,
      uniqueId,
      useSearch,
      refFiles,
      chatModel: chatModel || { modelType: DEFAULT_MODEL_TYPE },
      docId,
      expiredIndex: sessionHistory ? [...sessionHistory.expiredIndex] : [],
      clearContextIndex: sessionHistory
        ? [...sessionHistory.clearContextIndex]
        : [],
      vendor: isComposer ? "composer" : "chat",
      startTime,
    });

    // 发送给extension
  }, [disabled, clearSuggestQuestion, richEditorState, activeSessionId, loadingStatu, fileList, chatModel, promptTemplate.promptTemplate, sessionHistory, isComposer, setLoadingStatu, userInfo, useSearch, docId, abortCurrentChat, setSessionHistory, setActiveSession, summaryConversation, updateHistoryList, navigate]);

  const [minSize, setMinSize] = useState(112);

  useEffect(() => {
    const fileListEle = fileRef.current;
    if (fileListEle) {
      setMinSize(fileListEle.clientHeight + 112);
    }
    else {
      setMinSize(112);
    }
  }, [fileList]);

  return (
    <div
      className={clsx(
        "flex relative flex-col",
        wrapperClassName,
      )}
    >
      <div>
        {isComposer && <ApplyStatus />}
        <Resizable
          minHeight={`${minSize}px`}
          enable={props.enable ?? false}
          maxHeight={isComposer ? 340 : 376}
        >
          <div
            id="k-editor-container"
            className={clsx(
              css["k-editor-container"],
              "bg-input-background flex flex-col border border-dropdown-border hover:border-focusBorder",
              "rounded-lg",
              "transition-all",
              "relative",
              "h-full",
            )}
            style={{
              maxHeight: isComposer ? "340px" : "376px",
            }}
          >
            <div ref={panelRef}></div>
            {fileList.length
              ? (
                  <div
                    className="rounded-t-[7.5px] border-b border-border-common"
                    ref={fileRef}
                  >
                    <CustomScrollBar suppressScrollX className="pt-2 max-h-[132px]">
                      <div className="px-3 pb-2 grid grid-cols-2 gap-[6px] ">
                        {fileList.map((file, idx) => {
                          return (
                            <FileCard
                              file={file}
                              key={idx}
                              remove={() => removeFile(file)}
                            >
                            </FileCard>
                          );
                        })}
                      </div>
                    </CustomScrollBar>
                  </div>
                )
              : null}
            <RichEditor
              editorRef={editorRef}
              customOptions={customOptions}
              onSubmit={onSubmit}
              panelRef={panelRef}
              optRef={optRef}
              changeEditorState={changeEditorState}
              clearRef={clearRef}
              insertSignRef={insertSignRef}
              sharpPluginRef={sharpPluginRef}
              slashPluginRef={slashPluginRef}
              disabled={disabled}
              editorClassName={editorClassName}
              className={clsx(
                "flex-auto relative overflow-y-auto",
                {
                  "rounded-t-[7.5px] ": !fileList.length,
                },
              )}
              placeholder={placeholder}
              focused={focused}
              onFocusedChange={setFocused}
            />
            <div ref={optRef} className=""></div>
            <div className="w-full bg-settings-focusedRowBackground py-1 px-1.5 rounded-b-[7.5px]">
              <ModelArea richEditorState={richEditorState}></ModelArea>
            </div>
          </div>
        </Resizable>
      </div>
    </div>
  );
};
