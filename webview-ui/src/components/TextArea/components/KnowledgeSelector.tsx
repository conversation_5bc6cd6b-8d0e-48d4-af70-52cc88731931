import { CustomScrollBar } from "@/components/CustomScrollbar";
import { useRecordStore } from "@/store/record";
import { reportUserAction } from "@/utils/weblogger";
import { ReportOpt } from "@shared/types/logger";
import { Popover, Tooltip } from "@/components/Union/chakra-ui";
import { PopoverContent, PopoverTrigger, useDisclosure } from "@chakra-ui/react";
import { clsx } from "clsx";
import { useMemo } from "react";
import ClearKnowledgeIcon from "@/assets/icons/knowledge-clear.svg?react";
import ArrowDownIcon from "@/assets/icons/arrow-up.svg?react";
import KnowledgeDisableIcon from "@/assets/icons/knowledge-disable.svg?react";
import KnowledgeSelectIcon from "@/assets/icons/knowledge-select.svg?react";
import css from "./modelArea.module.less";
import { Doc } from "@shared/types/business";

export const KnowledgeSelector: React.FC = () => {
  const docList = useRecordStore(state => state.docList);
  const { onOpen, onClose, isOpen, onToggle } = useDisclosure();
  const docId = useRecordStore(state => state.docId);
  const setDocId = useRecordStore(state => state.setDocId);

  const doc = docList.find(item => item.id === docId);

  const selectKnowledge = (doc: Doc) => {
    const params: ReportOpt<"chSetting"> = {
      key: "chSetting",
      type: "knRepo",
      content: doc.id.toString(),
    };
    reportUserAction(params);
    const id = Number(doc.id);
    setDocId(id);
    onClose();
  };

  const clearKnowledge = () => {
    setDocId(0);
    onClose();
  };

  const clearDoc = () => {
    setDocId(0);
  };

  const KnowledgeTrigger = useMemo(() => {
    const doc = docList.find(item => item.id === docId);
    const name = doc?.name || "知识库";

    return (
      <div
        className={clsx(
          "hover:bg-[var(--custom-menu-trigger-bg)] group flex items-center gap-1 cursor-pointer rounded px-1.5 py-1",
          {
            "bg-[var(--custom-menu-trigger-bg)]": isOpen || doc,
          },
        )}
        onMouseDown={e => e.preventDefault()}
        onClick={onToggle}
      >
        {!isOpen
          ? (
              <div className="w-4 h-4 flex items-center justify-center text-tab-inactiveForeground">
                {doc
                  ? (
                      <KnowledgeSelectIcon className="group-hover:hidden text-button-secondaryForeground hover:"></KnowledgeSelectIcon>
                    )
                  : (
                      <KnowledgeDisableIcon className="group-hover:hidden "></KnowledgeDisableIcon>
                    )}
                <ArrowDownIcon
                  className={clsx(
                    "hidden group-hover:block rotate-180 group-hover:text-button-secondaryForeground",
                  )}
                >
                </ArrowDownIcon>
              </div>
            )
          : (
              <div
                className={clsx(
                  "w-4 h-4 flex items-center justify-center transition-all text-button-secondaryForeground",
                  {
                    "rotate-180": !isOpen,
                  },
                )}
              >
                <ArrowDownIcon></ArrowDownIcon>
              </div>
            )}
        <span
          className={clsx(
            // !showText && css["model-area-item-text-hidden"],
            "text-[13px]",
            css["model-area-item-text-hidden"],
            "truncate",
            "group-hover:text-button-secondaryForeground",
            (isOpen || doc)
              ? "text-button-secondaryForeground"
              : "text-tab-inactiveForeground",
          )}
        >
          {name}
        </span>
      </div>
    );
  }, [docId, docList, isOpen, onToggle]);

  return (
    <Popover
      isOpen={isOpen}
      onOpen={onOpen}
      onClose={onClose}
      strategy="fixed"
      placement="top-start"
    >
      <PopoverTrigger>
        <div>
          <Tooltip label={docId ? `已关联{${doc?.name}}` : "关联知识库"}>
            {KnowledgeTrigger}
          </Tooltip>
        </div>
      </PopoverTrigger>
      <PopoverContent
        border="none"
        w="200px"
        sx={{
          "&:focus": {
            outline: "none",
          },
          "&:focus-visible": {
            outline: "none",
            boxShadow: "none",
          },
        }}
      >
        <div className="flex flex-col border border-settings-dropdownBorder rounded bg-dropdown-background text-dropdown-foreground">
          <CustomScrollBar suppressScrollX className="h-[224px] w-[200px]">
            <div className="p-1 w-full flex flex-col gap-1 rounded">
              {docList.map((doc, idx) => {
                return (
                  <div
                    className={clsx(
                      "leading-[19.5px] text-[13px] w-full rounded-sm px-3 py-2 cursor-pointer",
                      {
                        "bg-list-activeSelectionBackground text-button-foreground":
                          docId === doc.id,
                        "hover:bg-list-inactiveSelectionBackground hover:text-list-inactiveSelectionForeground":
                          docId !== doc.id,
                      },
                    )}
                    onClick={() => {
                      selectKnowledge(doc);
                    }}
                    key={idx}
                  >
                    {doc.name}
                  </div>
                );
              })}
            </div>
          </CustomScrollBar>
          <div
            className="flex gap-1 h-9 items-center px-3 border-t border-menu-border"
            onClick={clearKnowledge}
          >
            <div className="w-4 h-4 flex justify-center items-center text-foreground">
              <ClearKnowledgeIcon></ClearKnowledgeIcon>
            </div>
            <span
              className="text-[13px] leading-[19.5px] text-foreground cursor-pointer"
              onClick={clearDoc}
            >
              清除已选项
            </span>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};
