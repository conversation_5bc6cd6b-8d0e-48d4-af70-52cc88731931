import clsx from "clsx";
import { forwardRef, useCallback, useContext, useEffect, useRef } from "react";
import { MenuIcon } from "./MenuIcon";
import { HighlightText } from "./Highlight";
import AutoTooltip from "@/components/AutoTooltip";
import { RichEditorBoxPanelData } from "../../const";
import { NewPanelContext } from "./NewPanel";
import { ITEM_DATA_ATTR, useCollection } from "./collection";
import { Spinner, useMergeRefs } from "@chakra-ui/react";

export interface MenuItemProps {
  data: RichEditorBoxPanelData;
  index: number;
  disabled: boolean;
  disabledMsg: string;
  children?: React.ReactNode;
  onBeforeSelect?: () => void;
}

export const MenuItem = forwardRef<HTMLButtonElement, MenuItemProps>(function MenuItem({
  data,
  index,
  disabled,
  disabledMsg,
  children,
  onBeforeSelect,
}, forwardedRef) {
  const option = data;
  const context = useContext(NewPanelContext);
  if (!context) {
    throw new Error("NewPanelContext is not provided");
  }
  const {
    onSelectIndexChange: setSelectIndex,
    handleSelectMenu: handleSelectMenuProp,
    query,
    selectIndex,
    subMenuState,
    isKeyboardEvent,
    submittingIndex,
  } = context;

  const handleSelectMenu = useCallback(
    () => {
      onBeforeSelect?.();
      handleSelectMenuProp(option, index);
    },
    [onBeforeSelect, handleSelectMenuProp, option, index],
  );

  const disableHoverEvent = isKeyboardEvent;

  const isSecondary = Boolean(subMenuState);

  const selected = index === selectIndex;

  const { _itemMap: itemMap } = useCollection();

  const menuRef = useRef<HTMLButtonElement>(null);

  const composedRef = useMergeRefs(menuRef, forwardedRef);

  useEffect(() => {
    itemMap.set(menuRef, {
      ref: menuRef,
      disabled: disabled,
      data: option,
    });
    return () => {
      itemMap.delete(menuRef);
    };
  }, [disabled, itemMap, menuRef, option]);

  const handleHoverSelect = useCallback(
    () => {
      if (!disabled && !disableHoverEvent) {
        setSelectIndex(index);
      }
    },
    [disabled, disableHoverEvent, index, setSelectIndex],
  );

  return (
    <button
      ref={composedRef}
      style={{
        /* FIXME: --vscode-editor-font-family 是否应当在全局设置 */
        fontFamily: isSecondary ? "var(--vscode-editor-font-family)" : "inherit",
      }}
      className={clsx(
        `group flex  items-center  px-3  w-full rounded-sm`,
        [isSecondary ? "h-[28px]" : "h-[36px]"],
        [isSecondary ? "leading-[16px]" : "leading-[19.5px]"],
        [selected ? "bg-list-hoverBackground" : ""],
        [
          disabled
            ? "cursor-not-allowed"
            : "cursor-pointer",
        ],
      )}
      disabled={disabled}
      onClick={handleSelectMenu}
      onMouseEnter={handleHoverSelect}
      onMouseDown={(e) => {
        e.preventDefault();
      }}
      {...{ [ITEM_DATA_ATTR]: "" }}
    >
      {children || (
        <>
          <div
            className={clsx(
              "overflow-hidden  mr-6 flex items-center",
              [
                disabled
                  ? "text-text-common-disable"
                  : "text-text-common-primary",
              ],
            )}
          >
            <MenuIcon menu={option} isSecondary={isSecondary} />
            <HighlightText text={option.title} highlight={query} />
            {option.titleSuffix}
          </div>
          <div
            className={clsx(
              "kwaipilot-rich-editor-menu-description",
              [
                disabled
                  ? ""
                  : selected
                    ? "flex"
                    : isSecondary
                      ? ""
                      : "hidden",
              ],
              "flex-auto overflow-hidden",
              [
                disabled
                  ? "text-text-common-disable"
                  : "text-text-common-secondary",
              ],
              ["justify-end", "flex"],
            )}
          >
            <AutoTooltip title={option.description} placement="top">
              {disabled
                ? disabledMsg
                : option.description}
            </AutoTooltip>
          </div>
          {submittingIndex === index && <Spinner ml="4px" size="xs" />}
        </>
      )}
    </button>
  );
});
