import { useContext, useEffect, useMemo, useRef } from "react";
import { EmptyText } from "./EmptyText";
import { MenuItem } from "./MenuItem";
import { NewPanelContext, useNewPanelContext } from "./NewPanel";
import { SharpCommand, SlashCommand } from "@shared/types";
import { RichEditorBoxPanelData, RichEditorBoxPanelDataCustomPrompt, RichEditorMenuType } from "../../const";
import { useRichEditPanelMenuStore } from "@/store/richEditorPanelMenu";
import { getSortFileOrDir } from "../../utils";
import { throwNeverError } from "@/utils/throwUnknownError";
import { Popover, Portal } from "@/components/Union/chakra-ui";
import { PopoverTrigger, PopoverContent, Link, Spinner } from "@chakra-ui/react";
import { ExternalLinkIcon } from "@chakra-ui/icons";
import { useDesignToken } from "@/hooks/useDesignToken";
import { useScrolling } from "react-use";
import { useCustomScrollBar } from "@/components/CustomScrollbar";
import { Env } from "@/http/env";
import { weblog } from "@/utils/weblogger";
import { getRootContainer } from "@/utils/dom";

const PROMPT_ARTIST_PLATFORM_URL = Env.IS_PROD
  ? "https://prompt-artist.corp.kuaishou.com"
  : "https://prompt-artist.staging.kuaishou.com";

export const CUSTOM_PROMPT_POPOVER_CONTENT_CLASS_NAME = "custom-prompt-popover-content";

function CustomPromptItem({
  item,
  index,
  isRecent,
}: {
  item: RichEditorBoxPanelDataCustomPrompt;
  index: number;
  isRecent: boolean;
}) {
  const { submittingIndex, selectIndex } = useNewPanelContext();

  const containerRef = useRef<HTMLElement>(getRootContainer());

  const { container: scrollContainer } = useCustomScrollBar();
  const isScrolling = useScrolling(scrollContainer);

  const menuItem = (
    <MenuItem
      index={index}
      data={item}
      disabled={false}
      disabledMsg=""
    >
      <span className=" flex-none text-text-common-primary font-medium">{item.raw.name}</span>
      {isRecent && index === selectIndex && (
        <span className=" h-[18px] bg-tag-bg-kwaipilot rounded ml-[6px]">
          <span className=" text-text-common-secondary text-[12px] scale-[0.83] whitespace-nowrap inline-block">最近</span>
        </span>
      )}
      <span className="pl-4 whitespace-nowrap text-ellipsis overflow-hidden text-text-common-tertiary text-[12px] ml-auto">{item.raw.content}</span>
      {submittingIndex === index && <Spinner ml="4px" size="xs" />}
    </MenuItem>
  );

  if (isScrolling) {
    return menuItem;
  }
  return (
    <Popover
      trigger="hover"
      placement="top-end"
      boundary="clippingParents"
      key={item.raw.id}
    >
      <PopoverTrigger>
        <div>
          {menuItem}
        </div>
      </PopoverTrigger>
      <Portal containerRef={containerRef}>
        <PopoverContent
          w="270px"
          maxW="70vw"
          maxH={200}
          tabIndex={-1}
          className={CUSTOM_PROMPT_POPOVER_CONTENT_CLASS_NAME}
        >
          <div className=" p-2">
            <div className=" text-text-common-primary text-[13px] whitespace-nowrap leading-[20px] font-medium">{item.raw.name}</div>
          </div>
          <div className=" px-2 py-1 flex-auto overflow-y-auto">
            <div className=" text-text-common-secondary text-[12px] leading-[18px]">
              {item.raw.content}
            </div>
          </div>
          <div className=" px-2 py-[6px] flex justify-between items-center  border-0 border-t-[1px] border-t-border-horizontal">
            <div className=" text-text-common-secondary text-[12px] leading-[18px]">
              @
              {item.raw.owner_id}
            </div>
            <div className=" text-text-brand-default text-[12px] leading-[18px] ">
              <Link
                isExternal
                href={`${PROMPT_ARTIST_PLATFORM_URL}/playground?id=${item.raw.id}&bizCode=kwaipilot`}
                color="current"
              >
                提示词管理平台
                <ExternalLinkIcon color="current" mx="2px" />
              </Link>
            </div>
          </div>
        </PopoverContent>
      </Portal>
    </Popover>
  );
}

function MenuListCustomPrompt() {
  const { query, onOpenChange: setOpen, recentUsedPrompts } = useNewPanelContext();
  const panelMenuStore = useRichEditPanelMenuStore();

  const { tokens } = useDesignToken();

  const recentUsedPromptIds = useMemo(() => {
    return recentUsedPrompts.map(item => item.id);
  }, [recentUsedPrompts]);

  const customPromptsHitResult = useMemo(() => {
    return getSortFileOrDir(panelMenuStore.customPrompts, query);
  }, [panelMenuStore.customPrompts, query]);

  const recentUserdPrompts = useMemo(() => {
    return customPromptsHitResult.filter(item => recentUsedPromptIds.includes(item.raw.id));
  }, [customPromptsHitResult, recentUsedPromptIds]);
  const restPrompts = useMemo(() => {
    return customPromptsHitResult.filter(item => !recentUsedPromptIds.includes(item.raw.id));
  }, [customPromptsHitResult, recentUsedPromptIds]);

  useEffect(() => {
    if (query && panelMenuStore.customPrompts.length) {
      setOpen(true);
    }
  }, [query, panelMenuStore.customPrompts, setOpen]);

  // 添加曝光埋点
  useEffect(() => {
    weblog?.sendImmediately("SHOW", {
      action: "VS_CUSTOM_PROMPT_PANEL",
    });
  }, []);

  const noPropmpts = !panelMenuStore.customPrompts.length;

  const noHitQueryPrompts = query && !customPromptsHitResult.length;

  return (
    <>
      { noPropmpts || noHitQueryPrompts
        ? (
            <div className=" flex flex-col items-center py-[31px]">
              <img src="https://ali.a.yximgs.com/kos/nlav12119/lSiPplHt_2025-02-14-11-34-51.png" width={68}></img>
              <div className=" mt-2">
                暂无自定义指令，前往
                <Link
                  href={`${PROMPT_ARTIST_PLATFORM_URL}/index`}
                  textDecoration="none"
                  color={tokens.colorTextBrandDefault}
                  isExternal
                >
                  提示词管理平台
                </Link>
                新建
              </div>
            </div>
          )
        : (
            <>
              {recentUserdPrompts.map((item, index) => {
                return (
                  <CustomPromptItem
                    key={item.raw.id}
                    item={item}
                    index={index}
                    isRecent={true}
                  />
                );
              })}
              {recentUserdPrompts.length > 0 && restPrompts.length > 0 && <div className="h-[1px] bg-border-horizontal w-full"></div>}
              {restPrompts.map((item, index) => {
                return (
                  <CustomPromptItem
                    key={item.raw.id}
                    item={item}
                    index={index + recentUserdPrompts.length}
                    isRecent={false}
                  />
                );
              })}
            </>
          )}
    </>
  );
}

function MenuListCommon() {
  const { menuType, query, subMenuState: submenuState, onOpenChange: setOpen, open, filterSharpCommandKeyList } = useNewPanelContext();
  const panelMenuStore = useRichEditPanelMenuStore();

  const currentMenuItemList = useMemo<RichEditorBoxPanelData[]>(() => {
    if (menuType === RichEditorMenuType.SHARP_COMMAND) {
      if (!query) {
        const menuType2List = {
          [SharpCommand.FILE]: panelMenuStore.codeSearchDefaultFiles,
          [SharpCommand.FOLDER]: panelMenuStore.codeSearchDefaultDir,
          [SharpCommand.RULES]: panelMenuStore.ruleFiles,
        };
        return (submenuState
        /* 这里特殊处理 理论上 query 为空时 subMenu !== DIR_AND_FILE  但 useEffect 执行有先后顺序  */
          ? menuType2List[submenuState.key as keyof typeof menuType2List] || []
          : panelMenuStore.sharpCommand).filter(i => !filterSharpCommandKeyList.includes(i.key as SharpCommand));
      }
      const menuType2List = {
        [SharpCommand.FILE]: panelMenuStore.codeSearchWorkspaceFiles,
        [SharpCommand.FOLDER]: panelMenuStore.codeSearchWorkspaceDir,
        [SharpCommand.RULES]: panelMenuStore.ruleFiles,
      };
      // 处理带 query 的情况
      if (!submenuState) {
        let list = panelMenuStore.disabledMenu[SharpCommand.FOLDER]
          .status
          ? [...panelMenuStore.codeSearchWorkspaceFiles,
              ...panelMenuStore.ruleFiles]
          : [
              ...panelMenuStore.codeSearchWorkspaceDir,
              ...panelMenuStore.codeSearchWorkspaceFiles,
              ...panelMenuStore.ruleFiles,
            ];
        if (filterSharpCommandKeyList) {
          list = list.filter(i => !filterSharpCommandKeyList.includes(i.key as SharpCommand));
        }
        return getSortFileOrDir(list, query);
      }
      const rawList = menuType2List[submenuState.key as keyof typeof menuType2List] || [];
      return getSortFileOrDir(
        rawList.filter(i => !filterSharpCommandKeyList.includes(i.key as SharpCommand)),
        query,
      );
    }
    if (menuType === RichEditorMenuType.SLASH_COMMAND) {
      if (!query) {
        return panelMenuStore.slashCommand;
      }
      else {
        const res: RichEditorBoxPanelData[] = [];
        panelMenuStore.slashCommand.forEach((i) => {
          if (
            i.search?.some(i =>
              i.toLowerCase().startsWith(query.toLowerCase()),
            )
          ) {
            res.push(i);
          }
        });

        return res.filter(i => !filterSharpCommandKeyList.includes(i.key as SharpCommand));
      }
    }

    throwNeverError(menuType);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [panelMenuStore.disabledMenu[SharpCommand.FOLDER]
    .status, panelMenuStore.codeSearchDefaultDir, panelMenuStore.codeSearchDefaultFiles, filterSharpCommandKeyList, menuType, panelMenuStore.codeSearchWorkspaceDir, panelMenuStore.codeSearchWorkspaceFiles, panelMenuStore.sharpCommand, panelMenuStore.slashCommand, query, submenuState]);

  /* 延迟关闭， 只有 query 变化时连续两次都没有匹配的 item 时，才关闭 */
  const delayCloseState = useRef<{
    query: string;
    shouldClose: boolean;
  }>({
    query: "",
    shouldClose: false,
  });

  useEffect(() => {
    const oldState = delayCloseState.current;

    const isEmpty = currentMenuItemList.length === 0;
    if (query !== oldState.query && oldState.shouldClose && isEmpty && open) {
      setOpen(false);
    }
    delayCloseState.current = {
      query,
      shouldClose: isEmpty,
    };
  }, [query, currentMenuItemList, setOpen, open]);

  useEffect(() => {
    if (query && currentMenuItemList.length) {
      setOpen(true);
    }
  }, [query, currentMenuItemList, setOpen]);

  const subMenuTitle = useMemo(() => {
    switch (submenuState?.key) {
      case SharpCommand.FILE:
        return "文件";
      case SharpCommand.FOLDER:
        return "目录";
      case SharpCommand.RULES:
        return "规则";
      default:
        return "";
    }
  }, [submenuState]);

  return (
    <>
      {currentMenuItemList.map((option, index) => {
        return (
          <MenuItem
            key={index}
            data={option}
            index={index}
            disabled={panelMenuStore.disabledMenu[option.key]?.status}
            disabledMsg={panelMenuStore.disabledMenu[option.key]?.msg}
          />
        );
      })}
      {!currentMenuItemList.length && (
        <EmptyText
          subMenuTitle={subMenuTitle}
          isSecondary={Boolean(submenuState)}
        />
      )}
    </>
  );
}

export function MenuList() {
  const context = useContext(NewPanelContext);
  if (!context) {
    throw new Error("NewPanelContext is not provided");
  }
  const { menuType, subMenuState: submenuState } = context;

  return menuType === RichEditorMenuType.SLASH_COMMAND && submenuState?.key === SlashCommand.CUSTOM_PROMPT
    ? <MenuListCustomPrompt />
    : <MenuListCommon />;
}
