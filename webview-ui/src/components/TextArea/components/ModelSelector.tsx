import { CustomScrollBar } from "@/components/CustomScrollbar";
import { useRecordStore } from "@/store/record";
import { geActiveChatModel } from "@/utils/sessionUtils";
import { Popover, Tooltip } from "@/components/Union/chakra-ui";
import { PlacementWithLogical, PopoverContent, PopoverTrigger, useDisclosure } from "@chakra-ui/react";
import clsx from "clsx";
import { useMemo } from "react";
import ArrowDownIcon from "@/assets/icons/arrow-up.svg?react";
import { reportUserAction } from "@/utils/weblogger";
import { ReportOpt } from "@shared/types/logger";
import css from "./modelArea.module.less";
import { IChatModelType, Model } from "@shared/types/business";

type ModelSelectorProps = {
  placement: PlacementWithLogical;
  children?: React.ReactNode;
  trigger?: "click" | "hover";
  selectModelCallback?: (modelType: IChatModelType) => void;
  className?: string;
  activeBgColor?: string;
};

export const ModelSelector: React.FC<ModelSelectorProps> = ({
  placement,
  children,
  trigger,
  selectModelCallback,
  className,
  activeBgColor,
}: ModelSelectorProps) => {
  const { onOpen, onClose, onToggle, isOpen } = useDisclosure();
  const chatModel = useRecordStore(state => state.chatModel);
  const modelList = useRecordStore(state => state.modelList);
  const setChatModelType = useRecordStore(state => state.setChatModelType);
  const selectModel = (model: Model) => {
    const prams: ReportOpt<"chSetting"> = {
      key: "chSetting",
      type: "modelType",
      content: model.modelType.toString(),
    };
    reportUserAction(prams);
    setChatModelType(model.modelType);
  };
  const handleSelect = (model: Model) => {
    if (model.disabled) {
      return;
    }
    const modelType = model.modelType;
    if (selectModelCallback) {
      selectModelCallback(modelType);
    }
    else {
      selectModel(model);
    }
    onClose();
  };

  /** 当前使用的模型 */
  const chatModelDetail = useMemo(
    () => geActiveChatModel(modelList, chatModel.modelType),
    [chatModel.modelType, modelList],
  );

  return (
    <Popover
      isOpen={isOpen}
      onOpen={onOpen}
      onClose={onClose}
      strategy="fixed"
      placement={placement}
      closeOnBlur={true}
      trigger={trigger}
      boundary="scrollParent"
      computePositionOnMount={true}
      eventListeners={true}
      flip={true}
    >
      <PopoverTrigger>
        <div
          onMouseDown={e => e.preventDefault()}
          className={clsx(
            "flex-auto overflow-hidden flex gap-1 items-center cursor-pointer rounded hover:bg-button-secondaryHoverBackground group",
            {
              [activeBgColor ?? "bg-button-secondaryHoverBackground"]: isOpen,
            },
            className,
          )}
          onClick={onToggle}
        >
          {children
            ? (
                children
              )
            : (
                <>
                  <div
                    className={clsx(
                      "w-4 h-4 flex items-center justify-center transition-all text-button-secondaryForeground",
                    )}
                  >
                    {!isOpen
                      ? (
                          <>
                            <img
                              src={chatModelDetail?.icon}
                              className="w-4 h-4 block group-hover:hidden"
                              alt=""
                            />
                            <ArrowDownIcon className="hidden group-hover:block rotate-180" />
                          </>
                        )
                      : (
                          <ArrowDownIcon />
                        )}
                  </div>

                  <span
                    className={clsx(
                      "whitespace-nowrap group-hover:text-button-secondaryForeground text-[13px] leading-[19.5px] ",
                      css["model-area-item-text-hidden"],
                      "truncate",
                      isOpen
                        ? "text-button-secondaryForeground"
                        : "text-tab-inactiveForeground",
                    )}
                  >
                    {chatModelDetail?.name}
                  </span>
                </>
              )}
        </div>
      </PopoverTrigger>
      <PopoverContent
        border="none"
        w="200px"
        sx={{
          "&:focus": {
            outline: "none",
          },
          "&:focus-visible": {
            outline: "none",
            boxShadow: "none",
          },
        }}
      >
        <CustomScrollBar
          suppressScrollX
          className="h-[224px] w-[220px] border border-settings-dropdownBorder rounded"
        >
          <div className="p-1 w-full flex flex-col gap-1 rounded bg-dropdown-background text-dropdown-foreground">
            {modelList.map((model, idx) => {
              return (
                <div
                  className={clsx(
                    "w-full rounded-sm px-3 py-2",
                    {
                      "bg-list-activeSelectionBackground":
                        chatModelDetail.modelType === model.modelType,
                      "hover:bg-list-inactiveSelectionBackground hover:text-list-inactiveSelectionForeground":
                        chatModelDetail.modelType !== model.modelType,
                    },
                    model.disabled ? "cursor-not-allowed" : "cursor-pointer",
                  )}
                  onClick={() => handleSelect(model)}
                  key={idx}
                >
                  <Tooltip label={model.tooltip}>
                    <div className="flex gap-1 items-center">
                      <img src={model.icon} className="w-[14px] h-[14px]" />
                      <div
                        className={clsx(" leading-[19.5px] text-[13px]", [
                          model.disabled
                            ? "text-text-common-disable"
                            : "",
                        ])}
                      >
                        {model.name}
                      </div>
                      {model.vipIcon && <img src={model.vipIcon} alt="" />}
                    </div>
                  </Tooltip>
                  <div
                    className={clsx(
                      "text-[13px] leading-[18px]",
                      model.disabled
                        ? "text-text-common-disable"
                        : "",
                    )}
                  >
                    {model.desc}
                  </div>
                </div>
              );
            })}
          </div>
        </CustomScrollBar>
      </PopoverContent>
    </Popover>
  );
};
