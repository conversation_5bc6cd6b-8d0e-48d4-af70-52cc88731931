import { useRecordStore } from "@/store/record";
import UploadFileIcon from "@/assets/icons/upload-file.svg?react";
import { useMemo } from "react";
import { reportUserAction } from "@/utils/weblogger";
import { ReportOpt } from "@shared/types/logger";
// import { ExtensionAction } from "@shared/types/channel/extension";
import { useUserStore } from "@/store/user";
import { Tooltip } from "@/components/Union/chakra-ui";
import { kwaiPilotBridgeAPI } from "@/bridge";

export const UploadFileBtn: React.FC = () => {
  const fileList = useRecordStore(state => state.fileList);
  const setFileList = useRecordStore(state => state.setFileList);
  const userInfo = useUserStore(state => state.userInfo);

  const handleUpload = async () => {
    if (!userInfo) {
      kwaiPilotBridgeAPI.showToast({
        level: "error",
        message: "请先登录",
      });
      return;
    }
    if (disable) return;
    const params: ReportOpt<"uploadFile"> = {
      key: "uploadFile",
      type: undefined,
    };
    reportUserAction(params);
    const { fileInfo: newFileList } = await kwaiPilotBridgeAPI.fs.uploadFile();
    setFileList([...fileList, ...newFileList]);
  };

  const disable = useMemo(() => {
    return !userInfo;
  }, [userInfo]);

  return (
    <>
      <Tooltip label="支持上传多个文件（总大小不超过 3MB），支持 .pdf、.docx、.txt 代码文件 等">
        <div
          className="cursor-pointer flex gap-1 items-center h-6 px-[6px] py-[3px] rounded hover:bg-toolbar-activeBackground"
          onClick={handleUpload}
        >
          <div className="h-4 w-4 flex items-center justify-center text-tab-inactiveForeground">
            <UploadFileIcon></UploadFileIcon>
          </div>
          <div className="text-[13px] text-tab-inactiveForeground"> 文件</div>
        </div>
      </Tooltip>
    </>
  );
};
