import LinkNetIcon from "@/assets/icons/link-net.svg?react";
import UnLineNetIcon from "@/assets/icons/unlink-net.svg?react";
import { useRecordStore } from "@/store/record";
import { Tooltip } from "@/components/Union/chakra-ui";
import clsx from "clsx";
import { useEffect, useMemo } from "react";
import { reportUserAction } from "@/utils/weblogger";
import { ReportOpt } from "@shared/types/logger";
import css from "./modelArea.module.less";
import { isMentionNode } from "../lexical/CommandNode";
import { findFirst } from "../lexical/traversal";
import { CommandType } from "@shared/types";
import { SerializedEditorState } from "lexical";

const nullDocIdList = [-1, 0];
export function LinkNet({ richEditorState }: { richEditorState: SerializedEditorState | undefined }) {
  const setUseSearch = useRecordStore(state => state.setUseSearch);
  const setDocId = useRecordStore(state => state.setDocId);
  const useSearch = useRecordStore(state => state.useSearch);
  const docId = useRecordStore(state => state.docId);
  const fileList = useRecordStore(state => state.fileList);
  const changeUseSearch = () => {
    if (disable) return;
    const prams: ReportOpt<"chSetting"> = {
      key: "chSetting",
      type: "webSearch",
      content: String(!useSearch),
    };
    reportUserAction(prams);
    if (!useSearch) {
      setDocId(0);
    }
    setUseSearch(!useSearch);
  };

  const disable = useMemo(() => {
    const haveSharpCommand = richEditorState && Boolean(findFirst(richEditorState, (node) => {
      return isMentionNode(node) && node.commandType === CommandType.SHARP;
    }));
    if (!nullDocIdList.includes(docId) || haveSharpCommand || fileList.length) {
      return true;
    }
    return false;
  }, [docId, richEditorState, fileList]);

  const webSearchTooltip = useMemo(() => {
    if (disable) {
      return "关联知识后自动关闭联网";
    }
    return useSearch
      ? "已开启联网，Kwaipilot 会在需要时通过互联网搜集资料"
      : "已关闭联网";
  }, [disable, useSearch]);

  useEffect(() => {
    const haveSharpCommand = richEditorState && Boolean(findFirst(richEditorState, (node) => {
      return isMentionNode(node) && node.commandType === CommandType.SHARP;
    }));
    if (!nullDocIdList.includes(docId) || haveSharpCommand || fileList.length) {
      setUseSearch(false);
    }
  }, [docId, setUseSearch, richEditorState, fileList]);

  return (
    <Tooltip label={webSearchTooltip}>
      <div
        className={clsx(
          "flex gap-1 items-center  py-1 px-1.5 rounded flex-auto overflow-hidden group",
          [disable ? "cursor-not-allowed" : "cursor-pointer"],
          { "hover:bg-button-secondaryHoverBackground": !disable },
        )}
        onClick={changeUseSearch}
      >
        <div
          className={clsx(
            {
              "text-tab-inactiveForeground": !disable,
              "group-hover:text-button-secondaryForeground": !disable,
              "text-disabledForeground": disable,
            },
          )}
        >
          {useSearch
            ? (
                <LinkNetIcon></LinkNetIcon>
              )
            : (
                <UnLineNetIcon></UnLineNetIcon>
              )}
        </div>
        <span
          className={clsx(
            "text-[13px] leading-[19.5px] ",
            {
              "text-tab-inactiveForeground": !disable,
              "group-hover:text-button-secondaryForeground": !disable,
              "text-disabledForeground": disable,
            },
            css["model-area-item-text-hidden"],
            "truncate",
          )}
        >
          {useSearch ? "已联网" : "未联网"}
        </span>
      </div>
    </Tooltip>
  );
};
