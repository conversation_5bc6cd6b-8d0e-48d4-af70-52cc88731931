import { Tooltip } from "@/components/Union/chakra-ui";
import { Box, useColorMode } from "@chakra-ui/react";

import { MentionNodeV2 } from "./MentionNodeV2";
import clsx from "clsx";

export const MENTION_NODE_V2_CLASS_NAME = `mention-node-v2-label`;

export function MentionNodeV2Label({ node }: { node: MentionNodeV2 }) {
  const tooltipContent = node.__structure.relativePath;
  const isDark = useColorMode().colorMode === "dark";
  return (
    <Tooltip openDelay={1000} placement="top" label={tooltipContent} maxW="90vw" wordBreak="break-all">
      <Box
        as="span"
        fontFamily="var(--vscode-editor-font-family)"
        className={clsx(MENTION_NODE_V2_CLASS_NAME,
          "rounded leading-[18px] py-[2px] bg-[#4A83F64D] text-[12px]  hover:bg-[#4A83F62D]",
        )}
        wordBreak="break-all"
        data-type={node.__structure.type}
        color={isDark ? "hsla(212, 100%, 76%,1)" : "hsl(212deg 86.66% 52.8%)"}
      >
        <span className="pl-1">
          #
        </span>
        <span className=" pr-1">
          {node.getTextContent()}
        </span>
      </Box>
    </Tooltip>
  );
}
