import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import {
  RefObject,
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import IconBeta from "@/assets/beta.svg?react";
import { NewPanel, PanelHeaderData } from "@/components/TextArea/components/NewPanel";
import {
  CommandPrefix,
  CommandType,
  DisableRichEditorMenu,
  SlashCommand,
} from "@shared/types";
import {
  getCommandQuery,
  getQueryTextForSearch,
} from "@/components/TextArea/utils";
import { useRichEditPanelMenuStore } from "@/store/richEditorPanelMenu";
import { usePromptTemplate } from "@/store/promptTemplate";
import { CommandPluginRef } from "@/components/TextArea";
import { httpClient } from "@/http";
import { RichEditorBoxPanelData, RichEditorMenuType } from "../const";
import { useRichEditorContext } from "../hooks/useRichEditorContext";
import { isMentionNode } from "./CommandNode";
import { findFirst } from "./traversal";

interface IProps {
  panelRef: RefObject<HTMLDivElement>;
  closeSharpPanel: () => void;
}

export const SlashPlugin = forwardRef<CommandPluginRef | undefined, IProps>(
  (props, ref) => {
    const {
      panelRef,
      closeSharpPanel,
    } = props;
    const [editor] = useLexicalComposerContext();
    const [shown, setShown] = useState(false);
    const richEditorPanelMenuStore = useRichEditPanelMenuStore();
    const setCustomPrompts = useRichEditPanelMenuStore(s => s.setCustomPrompts);
    const promptTemplateStore = usePromptTemplate();
    const readyClose = useRef(false);
    const [query, setQuery] = useState("");

    const {
      focused,
      setCommandShown,
    } = useRichEditorContext();

    const openPanel = useCallback(() => {
      const editorState = editor.getEditorState().toJSON();
      const firstSlashCommand = findFirst(editorState, (node) => {
        return isMentionNode(node) && node.commandType === CommandType.SLASH;
      });
      const firstSharpCommand = findFirst(editorState, (node) => {
        return isMentionNode(node) && node.commandType === CommandType.SHARP;
      });
      if (!firstSlashCommand && !firstSharpCommand) {
        closeSharpPanel();
        setShown(true);
      }
    }, [editor, closeSharpPanel]);

    const closePanel = useCallback(() => {
      setShown(false);
    }, []);

    const fetchPromptConfig = useCallback(() => {
      httpClient.getPlatformConfig().then((res) => {
        promptTemplateStore.setPromptTemplate(res.promptConfigs);
        const status: DisableRichEditorMenu = {};
        res.promptConfigs.forEach((r) => {
          status[CommandPrefix.SLASH + r.key] = {
            status: false,
            msg: "",
          };
        });
        richEditorPanelMenuStore.setDisabledMenu(status);
      });
    }, [promptTemplateStore, richEditorPanelMenuStore]);

    useEffect(() => {
      fetchPromptConfig();
      // FIXME: eslint
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
      if (shown) {
      /* {
        id: 1,
        name: "自定义指令1",
        content: `{{currentFile}}  {{currentDir}} {{repository}}参考当前选中代码 {{selection}}帮我用 {{language}} 实现一个无限列表。使用 {{ahooks_tool }}，滚动到底部时加载下一页；分页参数使用 {{pcursor}}，初始为 ' '，最后一页返回 'no_more'，每次请求时参数带上上次返回给我的 {{pcursor}}；支持关键词筛选，关键词参数为 {{keyword}}，输入改变时重新发起请求并防抖，列表每一项 UI 如下。`,
        owner_id: "1",
      },
      ...Array.from({ length: 30 }).map((_, index) => ({
        id: index + 2,
        name: `自定义指令${index + 2}`,
        content: `自定义指令${index + 2}内容`,
        owner_id: `User_${index + 2}`,
      })), */
        httpClient.getCustomPrompts().then((res) => {
          setCustomPrompts(res);
        });
      }
    }, [setCustomPrompts, shown]);

    useEffect(() => {
      readyClose.current = false;
    }, [shown]);

    useImperativeHandle(
      ref,
      () => {
        return {
          open: () => {
            if (!focused) {
              editor.focus();
            }
            openPanel();
          },
          close: () => {
            closePanel();
          },
          getShown: () => {
            return shown;
          },
        };
      },
      [shown, focused, editor, closePanel, openPanel],
    );

    const handleQueryCheck = useCallback(() => {
      const query = getCommandQuery(["/", "、"], getQueryTextForSearch(editor));
      if (query !== null) {
        setQuery(query);
        if (!shown && !query) {
          openPanel();
        }
        return;
      }
      setQuery("");
      closePanel();
    }, [editor, openPanel, closePanel, shown]);

    useEffect(() => {
      const updateListener = () => {
        return editor.getEditorState().read(() => {
          handleQueryCheck();
        });
      };

      const dispose = editor.registerUpdateListener(updateListener);
      return () => {
        dispose();
      };
    }, [editor, handleQueryCheck]);

    useEffect(() => {
      if (focused) {
        handleQueryCheck();
      }
    }, [handleQueryCheck, focused]);

    useEffect(() => {
      setCommandShown(prev => ({
        sharpShown: prev.sharpShown,
        slashShown: shown,
      }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [shown]);

    const [submenuState, setSubMenuState] = useState<RichEditorBoxPanelData | null>(null);

    const panelHeaderData = useMemo<PanelHeaderData | null>(() => {
      if (submenuState) {
        if (submenuState.key === SlashCommand.CUSTOM_PROMPT) {
          return {
            title: (
              <span className="flex items-center">
                自定义指令
                <span className=" ml-1">
                  <IconBeta />
                </span>
              </span>
            ),
            description: "继续输入进行搜索",
            icon: "",
          };
        }
        else {
          return null;
        }
      }
      return {
        title: "指令",
        description: "由 Kwaipilot RAG 提供服务",
        icon: "",
      };
    }, [submenuState]);

    return (
      <NewPanel
        open={shown}
        onOpenChange={setShown}
        query={query}
        menuType={RichEditorMenuType.SLASH_COMMAND}
        subMenuState={submenuState}
        onSubMenuStateChange={setSubMenuState}
        panelHeaderData={panelHeaderData}
        panelRef={panelRef}
      />
    );
  },
);

SlashPlugin.displayName = "SlashPlugin";
