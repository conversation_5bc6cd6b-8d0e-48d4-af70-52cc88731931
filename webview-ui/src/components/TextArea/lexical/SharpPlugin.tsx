import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import {
  RefObject,
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from "react";
import {
  CommandType,
  SharpCommand,
} from "@shared/types";
import { useRichEditPanelMenuStore } from "@/store/richEditorPanelMenu";
import { NewPanel } from "@/components/TextArea/components/NewPanel";
import { useColorMode } from "@chakra-ui/react";
import {
  getCommandQuery,
  getQueryTextForSearch,
  getSlashCommand,
  getSortFileOrDir,
} from "@/components/TextArea/utils";
import { CommandPluginRef } from "@/components/TextArea";
import { RichEditorBoxPanelData, RichEditorMenuType } from "@/components/TextArea/const";
import { SubMenuTitleIcon } from "@/components/TextArea/components/SubMenuTitleIcon";
import repoChatService from "@/services/repo-chat";
import { useRichEditorContext } from "../hooks/useRichEditorContext";
import { SerializedEditorState, SerializedLexicalNode } from "lexical";
import { SerializedMentionNode } from "./CommandNode";
import { traversePreOrder } from "./traversal";

interface IProps {
  panelRef: RefObject<HTMLDivElement>;
  closeSlashPanel: () => void;
  filterSharpCommandKeyList?: SharpCommand[];
}

export const SharpPlugin = forwardRef<CommandPluginRef | undefined, IProps>(
  (props, ref) => {
    const {
      panelRef,
      closeSlashPanel,
      filterSharpCommandKeyList = [],
    } = props;

    const [editor] = useLexicalComposerContext();
    const {
      focused,
      setCommandShown,
    } = useRichEditorContext();

    const [shown, setShown] = useState(false);
    const [query, setQuery] = useState("");
    const richEditorPanelMenuStore = useRichEditPanelMenuStore();
    const [subMenuState, setSubMenuState] = useState<RichEditorBoxPanelData | null>(null);

    const panelMenuStore = useRichEditPanelMenuStore();

    const { colorMode: theme } = useColorMode();
    const isDark = theme === "dark";

    const openPanel = useCallback(() => {
      const slashCommand = getSlashCommand(editor.getEditorState().toJSON());
      if (!slashCommand) {
        closeSlashPanel();
        setShown(true);
      }
    }, [editor, closeSlashPanel, setShown]);

    const closePanel = useCallback(() => {
      if (shown) {
        setShown(false);
      }
    }, [setShown, shown]);

    const currentMenuItemList = useMemo<RichEditorBoxPanelData[]>(() => {
      if (!query) {
        const menuType2List = {
          [SharpCommand.FILE]: panelMenuStore.codeSearchDefaultFiles,
          [SharpCommand.FOLDER]: panelMenuStore.codeSearchDefaultDir,
          [SharpCommand.RULES]: panelMenuStore.ruleFiles,
        };
        return (subMenuState
        /* 这里特殊处理 理论上 query 为空时 subMenu !== DIR_AND_FILE  但 useEffect 执行有先后顺序  */
          ? menuType2List[subMenuState.key as keyof typeof menuType2List] || []
          : panelMenuStore.sharpCommand).filter(i => !filterSharpCommandKeyList.includes(i.key as SharpCommand));
      }
      const menuType2List = {
        [SharpCommand.FILE]: panelMenuStore.codeSearchWorkspaceFiles,
        [SharpCommand.FOLDER]: panelMenuStore.codeSearchWorkspaceDir,
        [SharpCommand.RULES]: panelMenuStore.ruleFiles,
      };
      // 处理带 query 的情况
      if (!subMenuState) {
        const list = panelMenuStore.disabledMenu[SharpCommand.FOLDER]
          .status
          ? [...panelMenuStore.codeSearchWorkspaceFiles,
              ...panelMenuStore.ruleFiles,
            ]
          : [
              ...panelMenuStore.codeSearchWorkspaceDir,
              ...panelMenuStore.codeSearchWorkspaceFiles,
              ...panelMenuStore.ruleFiles,
            ];
        return getSortFileOrDir(list, query).filter(i => !filterSharpCommandKeyList.includes(i.key as SharpCommand));
      }
      const rawList = menuType2List[subMenuState.key as keyof typeof menuType2List] || [];
      return getSortFileOrDir(
        rawList.filter(i => !filterSharpCommandKeyList.includes(i.key as SharpCommand)),
        query,
      );
    }, [query, panelMenuStore.codeSearchWorkspaceFiles, panelMenuStore.codeSearchWorkspaceDir, panelMenuStore.codeSearchDefaultFiles, panelMenuStore.codeSearchDefaultDir, panelMenuStore.sharpCommand, panelMenuStore.disabledMenu, panelMenuStore.ruleFiles, subMenuState, filterSharpCommandKeyList]);

    useImperativeHandle(
      ref,
      () => {
        return {
          close: closePanel,
          open: () => {
            if (!focused) {
              editor.focus();
            }
            setTimeout(() => {
              openPanel();
            });
          },
          getShown: () => {
            return shown;
          },
        };
      },
      [shown, focused, editor, closePanel, openPanel],
    );

    const fetchDefaultFileAndDir = useCallback(async () => {
      const { files, dirs }
        = await repoChatService.getWorkspaceFileListAndDirList();
      richEditorPanelMenuStore.setCodeSearchDefaultFiles(files);
      richEditorPanelMenuStore.setCodeSearchDefaultDir(dirs);
    }, [richEditorPanelMenuStore]);

    useEffect(() => {
      if (!query && shown) {
        fetchDefaultFileAndDir();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [shown]);

    const handleQueryCheck = useCallback(() => {
      const queryText = getCommandQuery(["#"], getQueryTextForSearch(editor));
      if (queryText !== null) {
        setQuery(queryText);
        if (currentMenuItemList.length && !shown) {
          openPanel();
        }
      }
      else {
        setQuery("");
        closePanel();
      }
    }, [editor, currentMenuItemList.length, shown, openPanel, closePanel]);

    /** 获取query */
    useEffect(() => {
      const dispose = editor.registerUpdateListener(({ editorState }) => {
        editorState.read(() => {
          handleQueryCheck();
        });
      });
      return () => dispose();
    }, [editor, handleQueryCheck]);

    useEffect(() => {
      if (focused) {
        handleQueryCheck();
      }
    }, [focused, handleQueryCheck]);

    useEffect(() => {
      setCommandShown(prev => ({
        sharpShown: shown,
        slashShown: prev.slashShown,
      }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [shown]);

    const panelHeaderData = useMemo(() => {
      if (subMenuState) {
        if (subMenuState.key === SharpCommand.FILE) {
          return {
            title: "文件",
            description: "继续输入进行搜索",
            icon: (
              <SubMenuTitleIcon
                isDark={isDark}
                submenu={subMenuState}
              >
              </SubMenuTitleIcon>
            ),
          };
        }
        else if (subMenuState.key === SharpCommand.FOLDER) {
          return {
            title: "目录",
            description: "继续输入进行搜索",
            icon: (
              <SubMenuTitleIcon
                isDark={isDark}
                submenu={subMenuState}
              >
              </SubMenuTitleIcon>
            ),
          };
        }
        else if (subMenuState.key === SharpCommand.RULES) {
          return {
            title: "规则",
            description: "继续输入进行搜索",
            icon: (
              <SubMenuTitleIcon
                isDark={isDark}
                submenu={subMenuState}
              >
              </SubMenuTitleIcon>
            ),
          };
        }
        else {
          return null;
        }
      }
      return {
        title: "知识",
        description: "由 Kwaipilot RAG 提供服务",
        icon: "",
      };
    }, [isDark, subMenuState]);

    return (
      <NewPanel
        query={query}
        menuType={RichEditorMenuType.SHARP_COMMAND}
        subMenuState={subMenuState}
        onSubMenuStateChange={setSubMenuState}
        panelHeaderData={panelHeaderData}
        panelRef={panelRef}
        open={shown}
        onOpenChange={setShown}
        filterSharpCommandKeyList={filterSharpCommandKeyList}
      />
    );
  },
);
SharpPlugin.displayName = "SharpPlugin";

function isMentionNode(node: SerializedLexicalNode): node is SerializedMentionNode {
  return node.type === "mention";
}

function isSharpCommandMentionNode(node: SerializedMentionNode): node is SerializedMentionNode {
  return node.type === "mention" && node.commandType === CommandType.SHARP;
}
/**
 * 从用户输入中获取其使用 #指令的记录
 */
export function collectSharpCommandLog(editorState: SerializedEditorState): string {
  const commands = new Set<string>();
  const COMMAND_LOG_MAP: Record<SharpCommand, string> = {
    [SharpCommand.CURRENT_FILE]: "当前文件",
    [SharpCommand.FILE]: "文件",
    [SharpCommand.FOLDER]: "目录",
    [SharpCommand.CODEBASE]: "代码仓库",
    [SharpCommand.RULES]: "规则",
  };
  for (const node of traversePreOrder(editorState.root)) {
    if (isMentionNode(node) && isSharpCommandMentionNode(node)) {
      commands.add(COMMAND_LOG_MAP[node.key as SharpCommand]);
    }
  }
  return Array.from(commands).join(",");
}

export function haveSharpCommand(editorState: SerializedEditorState): boolean {
  const root = editorState.root;

  for (const node of traversePreOrder(root)) {
    if (isMentionNode(node) && isSharpCommandMentionNode(node)) {
      return true;
    }
  }

  return false;
}
