import { DialogSetting } from "@/store/record";
import {
  $createRangeSelection,
  $createTextNode,
  $getRoot,
  $setSelection,
  ParagraphNode,
  SerializedEditorState,
  SerializedLexicalNode,
} from "lexical";
import { RefObject, useCallback, useMemo } from "react";
import { getSlashCommand } from "@/components/TextArea/utils";
import { CommandPluginRef } from "@/components/TextArea";
import ReactDOM from "react-dom";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { UploadFileBtn } from "@/components/TextArea/components/UploadFileBtn";
import clsx from "clsx";
import SendFlyIcon from "@/assets/icons/send-fly.svg?react";
import StopIcon from "@/assets/icons/stop-circle.svg?react";
import { useColorMode } from "@chakra-ui/react";
import { useRichEditorContext } from "../hooks/useRichEditorContext";
import { findFirst } from "./traversal";
import { isMentionNode } from "./CommandNode";
import { CommandType } from "@shared/types";

interface IProps {
  setting?: DialogSetting;
  onSubmit: () => unknown;
  onStop?: () => unknown;
  richEditorState?: SerializedEditorState<SerializedLexicalNode>;
  insertSignRef: RefObject<{ insertSign: (sign: string) => void } | undefined>;
  disabled: boolean;
  sharpPluginRef: RefObject<CommandPluginRef | undefined>;
  slashPluginRef: RefObject<CommandPluginRef | undefined>;
  optRef: RefObject<HTMLDivElement>;
  moreOpt?: React.ReactNode;
  focused: boolean;
  slashCommandEnabled?: boolean;
  sharpCommandEnabled?: boolean;
  uploadFileEnabled?: boolean;
  loading?: boolean;
}

export const OptPlugin: React.FC<IProps> = (props: IProps) => {
  const {
    onSubmit,
    onStop,
    disabled,
    insertSignRef,
    optRef,
    moreOpt,
    slashCommandEnabled,
    sharpCommandEnabled,
    uploadFileEnabled,
    loading,
  } = props;
  const [editor] = useLexicalComposerContext();
  const { colorMode: theme } = useColorMode();
  const { commandShown } = useRichEditorContext();

  const isDark = theme === "dark";
  const richEditorState = editor.getEditorState().toJSON();

  const sharpClick = useCallback(() => {
    const slashCommand = getSlashCommand(richEditorState);
    if (!slashCommand) {
      insertSignRef.current?.insertSign("#");
    }
  }, [richEditorState, insertSignRef]);

  const sendBtnBg = useMemo(() => {
    if (isDark) {
      if (disabled) {
        return "bg-toolbar-activeBackground";
      }
      return "";
    }

    if (disabled) {
      return "bg-toolbar-activeBackground";
    }
    return "";
  }, [disabled, isDark]);

  const sendBtnIconColor = useMemo(() => {
    if (!isDark) {
      return "text-input-background";
    }

    if (disabled) {
      return "text-tab-inactiveForeground";
    }
    return "text-button-secondaryForeground";
  }, [disabled, isDark]);

  const slashClick = useCallback(() => {
    const editorState = editor.getEditorState().toJSON();
    const firstSlashCommand = findFirst(editorState, (node) => {
      return isMentionNode(node) && node.commandType === CommandType.SLASH;
    });
    const firstSharpCommand = findFirst(editorState, (node) => {
      return isMentionNode(node) && node.commandType === CommandType.SHARP;
    });
    if (!firstSharpCommand && !firstSlashCommand) {
      editor.update(() => {
        const root = $getRoot(); // 获取根节点的对象
        let firstNode = root.getFirstChild<ParagraphNode>(); // 获取第一个子节点
        if (!firstNode) {
          firstNode = new ParagraphNode();
          root.append(firstNode);
        }
        const firstChild = firstNode.getFirstChild();
        const textNode = $createTextNode("/");
        if (firstChild) {
          firstChild.insertBefore(textNode);
        }
        else {
          firstNode.append(textNode);
        }
        const selection = $createRangeSelection();
        selection.setTextNodeRange(textNode, 1, textNode, 1);
        $setSelection(selection);
      });
    }
  }, [editor]);

  return (
    optRef.current
    && ReactDOM.createPortal(
      <div className="rounded-b mt-1 w-full px-3 mb-3 z-10 h-[25px] flex items-center justify-between">
        <div className="flex gap-1 items-center">
          {(slashCommandEnabled ?? true) && (
            <div
              className={clsx(
                "flex h-6 px-[6px] py-[3px] items-center cursor-pointer text-[13px] hover:bg-toolbar-activeBackground text-tab-inactiveForeground rounded-[3px] ",
                {
                  "bg-toolbar-activeBackground": commandShown.slashShown,
                },
              )}
              onMouseDown={e => e.preventDefault()}
              onClick={slashClick}
            >
              / 指令
            </div>
          )}
          {(sharpCommandEnabled ?? true) && (
            <div
              className={clsx(
                "flex items-center cursor-pointer text-[13px]  hover:bg-toolbar-activeBackground text-tab-inactiveForeground  rounded-[3px] h-6 px-[6px] py-[3px]",
                {
                  "bg-toolbar-activeBackground": commandShown.sharpShown,
                },
              )}
              onClick={sharpClick}
              onMouseDown={e => e.preventDefault()}
            >
              # 知识
            </div>
          )}
          {(uploadFileEnabled ?? true) && <UploadFileBtn />}
          {moreOpt}
        </div>
        <button
          className={clsx(
            sendBtnBg,
            sendBtnIconColor,
            "flex justify-center items-center rounded p-1",
            [disabled ? "cursor-not-allowed" : "cursor-pointer"],
            [disabled ? "" : "bg-button-background"],
          )}
          disabled={disabled}
          onClick={() => {
            if (loading) {
              onStop?.();
            }
            else {
              onSubmit();
            }
          }}
        >
          {loading ? <StopIcon /> : <SendFlyIcon></SendFlyIcon>}
        </button>
      </div>,
      optRef.current,
    )
  );
};
