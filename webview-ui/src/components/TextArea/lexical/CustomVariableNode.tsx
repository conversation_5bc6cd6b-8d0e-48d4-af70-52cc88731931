import {
  $applyNodeReplacement,
  $getRoot,
  $isElementNode,
  type DOMConversionMap,
  type DOMConversionOutput,
  type DOMExportOutput,
  DecoratorNode,
  type EditorConfig,
  type LexicalEditor,
  type LexicalNode,
  type <PERSON>deK<PERSON>,
  TextNode,
} from "lexical";
import { CustomVariableLabel, MENTION_NODE_CLASS_NAME } from "./CustomVariableLabel";

import {
  CONTEXT_ITEM_MENTION_NODE_TYPE,
  customVariableItemMentionNodeDisplayText,
  serializeCustomVariableNode,
  SerializedCustomVariableNode,
  type SerializedCustomVariableItem,
} from "shared/lib/CustomVariable/nodes";
import { CustomVariableItem } from "shared";
import { DOM } from "@/utils/dom";

function convertContextItemMentionElement(domNode: HTMLElement): DOMConversionOutput | null {
  const data = domNode.getAttribute(DOM_DATA_ATTR);
  if (data !== null) {
    try {
      const contextItem: SerializedCustomVariableItem = JSON.parse(data);
      const node = $createCustomVariableNode(contextItem, { isFromInitialContext: false });
      return { node };
    }
    catch (error) {
      console.error(error);
      return null;
    }
  }

  return null;
}

const DOM_DATA_ATTR = "data-lexical-context-item-mention";

/**
 * New-style "chip" mention node.
 */
export class CustomVariableNode extends DecoratorNode<JSX.Element> {
  static override getType(): typeof CONTEXT_ITEM_MENTION_NODE_TYPE {
    return CONTEXT_ITEM_MENTION_NODE_TYPE;
  }

  static override clone(node: CustomVariableNode): CustomVariableNode {
    return new CustomVariableNode(
      node.getContextItem(),
      node.isFromInitialContext,
      node.key ?? node.__key,
    );
  }

  __contextItem: SerializedCustomVariableItem;

  getContextItem(): SerializedCustomVariableItem {
    const self = this.getLatest();
    return self.__contextItem;
  }

  setContextItem(contextItem: SerializedCustomVariableItem) {
    const self = this.getWritable();
    self.__contextItem = contextItem;
  }

  constructor(
    contextItemWithAllFields: CustomVariableItem | SerializedCustomVariableItem,
    public readonly isFromInitialContext: boolean,
    private key?: NodeKey,
  ) {
    super(key);
    this.__contextItem = serializeCustomVariableNode(contextItemWithAllFields);
  }

  override createDOM(): HTMLElement {
    return DOM.createElement("span");
  }

  override updateDOM(): boolean {
    return false;
  }

  override exportDOM(): DOMExportOutput {
    const element = DOM.createElement("span");
    element.setAttribute(DOM_DATA_ATTR, JSON.stringify(this.__contextItem));
    element.className = MENTION_NODE_CLASS_NAME;
    element.textContent = this.getTextContent();
    return { element };
  }

  static override importDOM(): DOMConversionMap | null {
    return {
      span: (domNode: HTMLElement) => {
        if (!domNode.hasAttribute(DOM_DATA_ATTR)) {
          return null;
        }
        return {
          conversion: convertContextItemMentionElement,
          priority: 1,
        };
      },
    };
  }

  static override importJSON(serializedNode: SerializedCustomVariableNode): CustomVariableNode {
    return $createCustomVariableNode(serializedNode.contextItem, {
      isFromInitialContext: serializedNode.isFromInitialContext,
    });
  }

  override exportJSON(): SerializedCustomVariableNode {
    return {
      contextItem: serializeCustomVariableNode(this.__contextItem),
      isFromInitialContext: this.isFromInitialContext,
      type: CustomVariableNode.getType(),
      text: this.getTextContent(),
      version: 1,
    };
  }

  override getTextContent(): string {
    return customVariableItemMentionNodeDisplayText(this.__contextItem);
  }

  override decorate(_editor: LexicalEditor, _config: EditorConfig): JSX.Element {
    return (
      <CustomVariableLabel
        node={this}
      />
    );
  }
}

export function $isCustomVariableNode(
  node: LexicalNode | null | undefined,
): node is CustomVariableNode {
  return node instanceof CustomVariableNode;
}

export function $createCustomVariableNode(
  contextItem: CustomVariableItem | SerializedCustomVariableItem,
  { isFromInitialContext }: { isFromInitialContext: boolean } = { isFromInitialContext: false },
): CustomVariableNode {
  const node = new CustomVariableNode(contextItem, isFromInitialContext);
  return $applyNodeReplacement(node);
}

export function $createVariableItemTextNode(contextItem: CustomVariableItem): TextNode {
  const textNode = new TextNode(customVariableItemMentionNodeDisplayText(serializeCustomVariableNode(contextItem)));
  return $applyNodeReplacement(textNode);
}

export function $getAllCustomVariableNode(): CustomVariableNode[] {
  const result: CustomVariableNode[] = [];
  function walk(node: LexicalNode) {
    if ($isCustomVariableNode(node)) {
      result.push(node);
    }
    else if ($isElementNode(node)) {
      node.getChildren().forEach(child => walk(child));
    }
  }
  const root = $getRoot();
  walk(root);
  return result;
}
