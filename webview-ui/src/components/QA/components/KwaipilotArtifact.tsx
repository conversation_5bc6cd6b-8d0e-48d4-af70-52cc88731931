import { kwaiPilotBridgeAPI } from "@/bridge";
import { useRecordStore } from "@/store/record";
import { useCallback, useContext } from "react";
import { IconArtifact } from "./IconArtifact";
import AutoTooltip from "@/components/AutoTooltip";
import { IconArtifactLoading } from "./IconArtifactLoading";
import { MarkdownRenderContext } from "./MarkdownRender";
import { ArtifactState } from "@/store/record.artifact";

export function KwaipilotArtifact({ messageId }: { messageId: string }) {
  const artifact: ArtifactState | undefined = useRecordStore(s => s.artifacts[messageId]);

  const loadingStatu = useRecordStore(s => s.loadingStatu);

  const renderContext = useContext(MarkdownRenderContext);

  if (!renderContext) {
    throw new Error("MarkdownCodeRenderer must be used within a MarkdownRender");
  }

  const isCurrentMessageLoading = loadingStatu?.status === "loading" && renderContext.qaItem.id === loadingStatu?.id;

  const handleArtifactClick = useCallback(() => {
    kwaiPilotBridgeAPI.previewArtifact({
      provider: "navi",
      artifact: {
        id: artifact.id,
        title: artifact.title,
        attr: {},
        actions: Object.values(artifact.actions).map(act => ({
          content: act.content,
          attr: {
            filePath: act.filePath,
            type: act.type,
          },
          closed: act.closed,

        })),
      },
    });
  }, [artifact]);

  if (!artifact) {
    return null;
  }
  const actionList = Object.values(artifact.actions);
  const pendingAction = actionList.find(act => !act.closed);
  return (
    <div className=" min-w-[200px] flex  items-start py-2 px-3 gap-1 border rounded-lg bg-bg-brand-active cursor-pointer" onClick={handleArtifactClick}>
      <div className=" mt-[2px] flex-none w-[32px] h-[32px] bg-bg-system-default flex items-center justify-center rounded-[4px]">
        <IconArtifact />
      </div>
      <div className="w-0 flex-auto">
        <div className=" text-text-common-primary text-[12px] leading-[18px] font-medium flex items-center gap-2">
          <AutoTooltip title={artifact.title} className=" truncate">
            {artifact.title || "未命名"}
          </AutoTooltip>
        </div>
        <div className="   text-text-common-secondary text-[12px] leading-[18px]">

          {
            pendingAction && !isCurrentMessageLoading
              ? <div>生成中断，请重试</div>
              : pendingAction
                ? (
                    <div className=" flex items-center gap-1">
                      <IconArtifactLoading className=" flex-none" />
                      <AutoTooltip title={pendingAction.filePath} className=" truncate">
                        {pendingAction.filePath}
                      </AutoTooltip>
                      <span className=" whitespace-nowrap">正在生成...</span>
                    </div>
                  )
                : actionList.length > 1
                  ? (
                      <span>
                        共&nbsp;
                        {actionList.length}
                  &nbsp;个文件
                      </span>
                    )
                  : actionList.length === 1
                    ? (
                        <AutoTooltip className=" truncate inline-block" title={actionList[0].filePath}>
                          {actionList[0].filePath}
                        </AutoTooltip>
                      )
                    : !artifact.closed
                      /* 没有 action 生成 但 artifact 还在生成中 说明第一个 action 还未就绪 */
                        ? (
                            <div className="flex items-center gap-1">
                              <IconArtifactLoading className=" flex-none" />
                              <span>生成中...</span>
                            </div>
                          )
                        : null
          }

        </div>
      </div>
    </div>
  );
}
