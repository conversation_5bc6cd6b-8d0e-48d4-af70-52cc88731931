.default-theme.light {
  --vscode-actionBar-toggledBackground: #dddddd;
--vscode-activityBar-activeBorder: #005fb8;
--vscode-activityBar-background: #f8f8f8;
--vscode-activityBar-border: #e5e5e5;
--vscode-activityBar-foreground: #1f1f1f;
--vscode-activityBar-inactiveForeground: #616161;
--vscode-activityBarBadge-background: #005fb8;
--vscode-activityBarBadge-foreground: #ffffff;
--vscode-badge-background: #cccccc;
--vscode-badge-foreground: #3b3b3b;
--vscode-button-background: #005fb8;
--vscode-button-border: #0000001a;
--vscode-button-foreground: #ffffff;
--vscode-button-hoverBackground: #0258a8;
--vscode-button-secondaryBackground: #e5e5e5;
--vscode-button-secondaryForeground: #3b3b3b;
--vscode-button-secondaryHoverBackground: #cccccc;
--vscode-chat-editedFileForeground: #895503;
--vscode-chat-slashCommandBackground: #d2ecff;
--vscode-chat-slashCommandForeground: #306ca2;
--vscode-checkbox-background: #f8f8f8;
--vscode-checkbox-border: #cecece;
--vscode-descriptionForeground: #3b3b3b;
--vscode-diffEditor-unchangedRegionBackground: #f8f8f8;
--vscode-dropdown-background: #ffffff;
--vscode-dropdown-border: #cecece;
--vscode-dropdown-foreground: #3b3b3b;
--vscode-dropdown-listBackground: #ffffff;
--vscode-editor-background: #ffffff;
--vscode-editor-foreground: #3b3b3b;
--vscode-editor-inactiveSelectionBackground: #e5ebf1;
--vscode-editor-selectionHighlightBackground: #add6ff80;
--vscode-editorGroup-border: #e5e5e5;
--vscode-editorGroupHeader-tabsBackground: #f8f8f8;
--vscode-editorGroupHeader-tabsBorder: #e5e5e5;
--vscode-editorGutter-addedBackground: #2ea043;
--vscode-editorGutter-deletedBackground: #f85149;
--vscode-editorGutter-modifiedBackground: #005fb8;
--vscode-editorIndentGuide-activeBackground1: #939393;
--vscode-editorIndentGuide-background1: #d3d3d3;
--vscode-editorLineNumber-activeForeground: #171184;
--vscode-editorLineNumber-foreground: #6e7681;
--vscode-editorOverviewRuler-border: #e5e5e5;
--vscode-editorSuggestWidget-background: #f8f8f8;
--vscode-editorWidget-background: #f8f8f8;
--vscode-errorForeground: #f85149;
--vscode-focusBorder: #005fb8;
--vscode-foreground: #3b3b3b;
--vscode-icon-foreground: #3b3b3b;
--vscode-input-background: #ffffff;
--vscode-input-border: #cecece;
--vscode-input-foreground: #3b3b3b;
--vscode-input-placeholderForeground: #767676;
--vscode-inputOption-activeBackground: #bed6ed;
--vscode-inputOption-activeBorder: #005fb8;
--vscode-inputOption-activeForeground: #000000;
--vscode-keybindingLabel-foreground: #3b3b3b;
--vscode-list-activeSelectionBackground: #e8e8e8;
--vscode-list-activeSelectionForeground: #000000;
--vscode-list-activeSelectionIconForeground: #000000;
--vscode-list-focusAndSelectionOutline: #005fb8;
--vscode-list-hoverBackground: #f2f2f2;
--vscode-menu-border: #cecece;
--vscode-menu-selectionBackground: #005fb8;
--vscode-menu-selectionForeground: #ffffff;
--vscode-notebook-cellBorderColor: #e5e5e5;
--vscode-notebook-selectedCellBackground: #c8ddf150;
--vscode-notificationCenterHeader-background: #ffffff;
--vscode-notificationCenterHeader-foreground: #3b3b3b;
--vscode-notifications-background: #ffffff;
--vscode-notifications-border: #e5e5e5;
--vscode-notifications-foreground: #3b3b3b;
--vscode-panel-background: #f8f8f8;
--vscode-panel-border: #e5e5e5;
--vscode-panelInput-border: #e5e5e5;
--vscode-panelTitle-activeBorder: #005fb8;
--vscode-panelTitle-activeForeground: #3b3b3b;
--vscode-panelTitle-inactiveForeground: #3b3b3b;
--vscode-peekViewEditor-matchHighlightBackground: #bb800966;
--vscode-peekViewResult-background: #ffffff;
--vscode-peekViewResult-matchHighlightBackground: #bb800966;
--vscode-pickerGroup-border: #e5e5e5;
--vscode-pickerGroup-foreground: #8b949e;
--vscode-ports-iconRunningProcessForeground: #369432;
--vscode-progressBar-background: #005fb8;
--vscode-quickInput-background: #f8f8f8;
--vscode-quickInput-foreground: #3b3b3b;
--vscode-searchEditor-textInputBorder: #cecece;
--vscode-settings-dropdownBackground: #ffffff;
--vscode-settings-dropdownBorder: #cecece;
--vscode-settings-headerForeground: #1f1f1f;
--vscode-settings-modifiedItemIndicator: #bb800966;
--vscode-settings-numberInputBorder: #cecece;
--vscode-settings-textInputBorder: #cecece;
--vscode-sideBar-background: #f8f8f8;
--vscode-sideBar-border: #e5e5e5;
--vscode-sideBar-foreground: #3b3b3b;
--vscode-sideBarSectionHeader-background: #f8f8f8;
--vscode-sideBarSectionHeader-border: #e5e5e5;
--vscode-sideBarSectionHeader-foreground: #3b3b3b;
--vscode-sideBarTitle-foreground: #3b3b3b;
--vscode-statusBar-background: #f8f8f8;
--vscode-statusBar-border: #e5e5e5;
--vscode-statusBar-debuggingBackground: #fd716c;
--vscode-statusBar-debuggingForeground: #000000;
--vscode-statusBar-focusBorder: #005fb8;
--vscode-statusBar-foreground: #3b3b3b;
--vscode-statusBar-noFolderBackground: #f8f8f8;
--vscode-statusBarItem-compactHoverBackground: #cccccc;
--vscode-statusBarItem-errorBackground: #c72e0f;
--vscode-statusBarItem-focusBorder: #005fb8;
--vscode-statusBarItem-hoverBackground: #b8b8b850;
--vscode-statusBarItem-prominentBackground: #6e768166;
--vscode-statusBarItem-remoteBackground: #005fb8;
--vscode-statusBarItem-remoteForeground: #ffffff;
--vscode-tab-activeBackground: #ffffff;
--vscode-tab-activeBorder: #f8f8f8;
--vscode-tab-activeBorderTop: #005fb8;
--vscode-tab-activeForeground: #3b3b3b;
--vscode-tab-border: #e5e5e5;
--vscode-tab-hoverBackground: #ffffff;
--vscode-tab-inactiveBackground: #f8f8f8;
--vscode-tab-inactiveForeground: #868686;
--vscode-tab-lastPinnedBorder: #d4d4d4;
--vscode-tab-selectedBackground: #ffffffa5;
--vscode-tab-selectedBorderTop: #68a3da;
--vscode-tab-selectedForeground: #333333b3;
--vscode-tab-unfocusedActiveBorder: #f8f8f8;
--vscode-tab-unfocusedActiveBorderTop: #e5e5e5;
--vscode-tab-unfocusedHoverBackground: #f8f8f8;
--vscode-terminal-foreground: #3b3b3b;
--vscode-terminal-inactiveSelectionBackground: #e5ebf1;
--vscode-terminal-tab-activeBorder: #005fb8;
--vscode-terminalCursor-foreground: #005fb8;
--vscode-textBlockQuote-background: #f8f8f8;
--vscode-textBlockQuote-border: #e5e5e5;
--vscode-textCodeBlock-background: #f8f8f8;
--vscode-textLink-activeForeground: #005fb8;
--vscode-textLink-foreground: #005fb8;
--vscode-textPreformat-background: #0000001f;
--vscode-textPreformat-foreground: #3b3b3b;
--vscode-textSeparator-foreground: #21262d;
--vscode-titleBar-activeBackground: #f8f8f8;
--vscode-titleBar-activeForeground: #1e1e1e;
--vscode-titleBar-border: #e5e5e5;
--vscode-titleBar-inactiveBackground: #f8f8f8;
--vscode-titleBar-inactiveForeground: #8b949e;
--vscode-welcomePage-tileBackground: #f3f3f3;
--vscode-widget-border: #e5e5e5;
}

.default-theme.dark {
   --vscode-actionBar-toggledBackground: #383a49;
--vscode-activityBar-activeBorder: #0078d4;
--vscode-activityBar-background: #181818;
--vscode-activityBar-border: #2b2b2b;
--vscode-activityBar-foreground: #d7d7d7;
--vscode-activityBar-inactiveForeground: #868686;
--vscode-activityBarBadge-background: #0078d4;
--vscode-activityBarBadge-foreground: #ffffff;
--vscode-badge-background: #616161;
--vscode-badge-foreground: #f8f8f8;
--vscode-button-background: #0078d4;
--vscode-button-border: #ffffff12;
--vscode-button-foreground: #ffffff;
--vscode-button-hoverBackground: #026ec1;
--vscode-button-secondaryBackground: #313131;
--vscode-button-secondaryForeground: #cccccc;
--vscode-button-secondaryHoverBackground: #3c3c3c;
--vscode-chat-editedFileForeground: #e2c08d;
--vscode-chat-slashCommandBackground: #34414b;
--vscode-chat-slashCommandForeground: #40a6ff;
--vscode-checkbox-background: #313131;
--vscode-checkbox-border: #3c3c3c;
--vscode-debugToolBar-background: #181818;
--vscode-descriptionForeground: #9d9d9d;
--vscode-dropdown-background: #313131;
--vscode-dropdown-border: #3c3c3c;
--vscode-dropdown-foreground: #cccccc;
--vscode-dropdown-listBackground: #1f1f1f;
--vscode-editor-background: #1f1f1f;
--vscode-editor-findMatchBackground: #9e6a03;
--vscode-editor-foreground: #cccccc;
--vscode-editor-inactiveSelectionBackground: #3a3d41;
--vscode-editor-selectionHighlightBackground: #add6ff26;
--vscode-editorGroup-border: #ffffff17;
--vscode-editorGroupHeader-tabsBackground: #181818;
--vscode-editorGroupHeader-tabsBorder: #2b2b2b;
--vscode-editorGutter-addedBackground: #2ea043;
--vscode-editorGutter-deletedBackground: #f85149;
--vscode-editorGutter-modifiedBackground: #0078d4;
--vscode-editorIndentGuide-activeBackground1: #707070;
--vscode-editorIndentGuide-background1: #404040;
--vscode-editorLineNumber-activeForeground: #cccccc;
--vscode-editorLineNumber-foreground: #6e7681;
--vscode-editorOverviewRuler-border: #010409;
--vscode-editorWidget-background: #202020;
--vscode-errorForeground: #f85149;
--vscode-focusBorder: #0078d4;
--vscode-foreground: #cccccc;
--vscode-icon-foreground: #cccccc;
--vscode-input-background: #313131;
--vscode-input-border: #3c3c3c;
--vscode-input-foreground: #cccccc;
--vscode-input-placeholderForeground: #989898;
--vscode-inputOption-activeBackground: #2489db82;
--vscode-inputOption-activeBorder: #2488db;
--vscode-keybindingLabel-foreground: #cccccc;
--vscode-list-activeSelectionIconForeground: #ffffff;
--vscode-list-dropBackground: #383b3d;
--vscode-menu-background: #1f1f1f;
--vscode-menu-border: #454545;
--vscode-menu-foreground: #cccccc;
--vscode-menu-selectionBackground: #0078d4;
--vscode-menu-separatorBackground: #454545;
--vscode-notificationCenterHeader-background: #1f1f1f;
--vscode-notificationCenterHeader-foreground: #cccccc;
--vscode-notifications-background: #1f1f1f;
--vscode-notifications-border: #2b2b2b;
--vscode-notifications-foreground: #cccccc;
--vscode-panel-background: #181818;
--vscode-panel-border: #2b2b2b;
--vscode-panelInput-border: #2b2b2b;
--vscode-panelTitle-activeBorder: #0078d4;
--vscode-panelTitle-activeForeground: #cccccc;
--vscode-panelTitle-inactiveForeground: #9d9d9d;
--vscode-peekViewEditor-background: #1f1f1f;
--vscode-peekViewEditor-matchHighlightBackground: #bb800966;
--vscode-peekViewResult-background: #1f1f1f;
--vscode-peekViewResult-matchHighlightBackground: #bb800966;
--vscode-pickerGroup-border: #3c3c3c;
--vscode-ports-iconRunningProcessForeground: #369432;
--vscode-progressBar-background: #0078d4;
--vscode-quickInput-background: #222222;
--vscode-quickInput-foreground: #cccccc;
--vscode-settings-dropdownBackground: #313131;
--vscode-settings-dropdownBorder: #3c3c3c;
--vscode-settings-headerForeground: #ffffff;
--vscode-settings-modifiedItemIndicator: #bb800966;
--vscode-sideBar-background: #181818;
--vscode-sideBar-border: #2b2b2b;
--vscode-sideBar-foreground: #cccccc;
--vscode-sideBarSectionHeader-background: #181818;
--vscode-sideBarSectionHeader-border: #2b2b2b;
--vscode-sideBarSectionHeader-foreground: #cccccc;
--vscode-sideBarTitle-foreground: #cccccc;
--vscode-statusBar-background: #181818;
--vscode-statusBar-border: #2b2b2b;
--vscode-statusBar-debuggingBackground: #0078d4;
--vscode-statusBar-debuggingForeground: #ffffff;
--vscode-statusBar-focusBorder: #0078d4;
--vscode-statusBar-foreground: #cccccc;
--vscode-statusBar-noFolderBackground: #1f1f1f;
--vscode-statusBarItem-focusBorder: #0078d4;
--vscode-statusBarItem-prominentBackground: #6e768166;
--vscode-statusBarItem-remoteBackground: #0078d4;
--vscode-statusBarItem-remoteForeground: #ffffff;
--vscode-tab-activeBackground: #1f1f1f;
--vscode-tab-activeBorder: #1f1f1f;
--vscode-tab-activeBorderTop: #0078d4;
--vscode-tab-activeForeground: #ffffff;
--vscode-tab-border: #2b2b2b;
--vscode-tab-hoverBackground: #1f1f1f;
--vscode-tab-inactiveBackground: #181818;
--vscode-tab-inactiveForeground: #9d9d9d;
--vscode-tab-lastPinnedBorder: #cccccc33;
--vscode-tab-selectedBackground: #222222;
--vscode-tab-selectedBorderTop: #6caddf;
--vscode-tab-selectedForeground: #ffffffa0;
--vscode-tab-unfocusedActiveBorder: #1f1f1f;
--vscode-tab-unfocusedActiveBorderTop: #2b2b2b;
--vscode-tab-unfocusedHoverBackground: #1f1f1f;
--vscode-terminal-foreground: #cccccc;
--vscode-terminal-inactiveSelectionBackground: #3a3d41;
--vscode-terminal-tab-activeBorder: #0078d4;
--vscode-textBlockQuote-background: #2b2b2b;
--vscode-textBlockQuote-border: #616161;
--vscode-textCodeBlock-background: #2b2b2b;
--vscode-textLink-activeForeground: #4daafc;
--vscode-textLink-foreground: #4daafc;
--vscode-textPreformat-background: #3c3c3c;
--vscode-textPreformat-foreground: #d0d0d0;
--vscode-textSeparator-foreground: #21262d;
--vscode-titleBar-activeBackground: #181818;
--vscode-titleBar-activeForeground: #cccccc;
--vscode-titleBar-border: #2b2b2b;
--vscode-titleBar-inactiveBackground: #1f1f1f;
--vscode-titleBar-inactiveForeground: #9d9d9d;
--vscode-welcomePage-progress-foreground: #0078d4;
--vscode-welcomePage-tileBackground: #2b2b2b;
--vscode-widget-border: #313131;
}
