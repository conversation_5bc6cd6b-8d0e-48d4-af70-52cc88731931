import eventBus from "@/utils/eventBus";
import {
  IChatModel,
  updateDialogSetting,
  genNewActiveSession,
  setSessionToLocal,
} from "@/utils/sessionUtils";
import { UploadFile } from "@shared/types/textarea";
import { createStore, StoreApi, useStore } from "zustand";
import { Doc, IChatModelType, Model } from "@shared/types/business";
import { DEFAULT_MODEL_TYPE } from "@/constant";
import { QAItem, SessionItem } from "@shared/types/chatHistory";
import { ContextExpiredTime } from "@shared/constant";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { WorkspaceState } from "@/services/repo-chat";
import { createContext, useContext, useMemo } from "react";
import { findModifiedFilesInMarkdown } from "@/components/QA/findModifiedFilesInMarkdown";
import { debounce, uniqBy, zipObject } from "lodash";
import { useComposerStatusStore } from "./composerStatus";
import { produce } from "immer";
import { setLocalStorageValue } from "@/utils/localStorage";
import {
  createRecordArtifactSlice,
  RecordArtifactSlice,
} from "./record.artifact";
import { httpClient } from "@/http";
import { useUserStore } from "./user";

// timeout 超时错误 error 一般错误
export type ILoadingStatus = "loading" | "timeout" | "error";

export interface ILoadingItem {
  id: string;
  status: ILoadingStatus;
}
export interface DialogSetting {
  id: string;
  setting: {
    modelType: IChatModelType;
    webSearch: boolean;
    knRepoId: number;
  };
}

export interface SelectCode {
  code: string;
  startLine: number;
  endLine: number;
  filename: string;
  /** 全路径 */
  path?: string;
  title: string;
}

export enum ReocrdStoreVendorEnum {
  chat = "chat",
  composer = "composer",
}
export type RecordStoreVendor =
  | keyof typeof ReocrdStoreVendorEnum
  | ReocrdStoreVendorEnum;
// 页面滚动的最小单元

export type IRecordStoreType = RecordArtifactSlice & RecordBaseSlice;
export interface RecordBaseSlice {
  // 当前 store 所属的 vendor
  vendor: RecordStoreVendor;
  activeSession: string;
  updateActiveSessionAndSessionHistory: (sessionId: string) => void;
  setActiveSession: (param: {
    value: string;
    updateLocalStorage?: boolean;
    navigateToChat?: boolean;
    updateHistory?: boolean;
  }) => void;
  // 正在加载/出错的对话Id数组
  loadingStatu: ILoadingItem | undefined;
  finishLoading: () => void;
  updateQAItem: (chatId: string, updater: (a: QAItem) => void) => void;
  /** 更，不传 status 为移除 */
  setLoadingStatu: (chatId: string, status?: ILoadingStatus) => void;
  getLoadingStatu: () => ILoadingItem | undefined;
  // 对话模型
  // chatType: IChatType;
  // setChatType: (value: IChatType, updateLocalStorage?: boolean) => void;
  // 是否联网搜索
  useSearch: boolean;
  setUseSearch: (value: boolean) => void;
  // resetRecordStore: () => void;
  chatModel: IChatModel;
  // 模型类型
  setChatModel: (value: IChatModel) => void;
  setChatModelType: (value?: IChatModelType) => void;
  // 知识库
  docId: number;
  setDocId: (value: number) => void;
  dialogSetting: DialogSetting[];
  setDialogSetting: (
    settingList: DialogSetting,
    updateLocalStorage?: boolean,
  ) => void;
  initSetting: (dialogSettings: DialogSetting[]) => void;
  fileList: UploadFile[];
  setFileList: (value: UploadFile[]) => void;
  removeFile: (file: UploadFile) => void;
  clearFileList: () => void;
  selectCode: SelectCode | null;
  setSelectCode: (value: SelectCode | null) => void;
  clearRecordSetting: () => void;

  modelList: Model[];
  docList: Doc[];
  setModelList: (value: Model[]) => void;
  setDocList: (value: Doc[]) => void;
  sessionHistory: SessionItem | undefined;
  setSessionHistory: (
    newsessionHistory: SessionItem,
    opt?: {
      /** 检查消息过期状态, 默认开启 */
      checkExpirationStatus?: boolean;
    }
  ) => void;
  clearSessionHistory: () => void;
  summaryConversation: boolean | undefined;
  setSummaryConversation: (v: boolean) => void;
  suggestQuestion: string[];
  setSuggestQuestion: (v: string[]) => void;
  clearSuggestQuestion: () => void;

  stopReceiveMessageId: string;
  setStopReceiveMessageId: (id: string) => void;

  currentChatAbortController: AbortController | null;
  abortCurrentChat: () => void;
  setCurrentChatAbortController: (controller: AbortController) => void;

  signalMessageDone: (chatId: string) => unknown;
  _replyBuffer: Record<string, string>;

  addReplyChunk: (chatId: string, payload: { replyContent: string }) => unknown;
};

type BearStore = ReturnType<typeof createRecordStore>;

export const RecordContext = createContext<BearStore | null>(null);
/**
 * hook 用于获取当前 context 中的 recordStore
 * @param selector
 * @returns
 */
export function useRecordStore<T>(selector: (state: IRecordStoreType) => T): T {
  const store = useContext(RecordContext);
  if (!store)
    throw new Error(
      "未发现 RecordContext.Provider, 提示: RecordContext 只会在 chat/composer 页面注入",
    );
  return useStore(store, selector);
}

/**
 * 获取任意 store, 注意: 不是 hook, 需要按下面方式使用
 *
 * ```
 * // example
 * const store = getStoreByVendor("chat")
 * const someState = useStore(store, state => state.someState)
 * ```
 * @param vendor
 * @returns
 */
export function getRecordStoreByVendor(vendor: RecordStoreVendor) {
  return recordStoreMap[vendor];
}

export function useRecoreStoreContext() {
  const context = useContext(RecordContext);
  if (!context)
    throw new Error(
      "未发现 RecordContext.Provider, 提示: RecordContext 只会在 chat/composer 页面注入",
    );
  return context;
}

type RecordProviderProps = React.PropsWithChildren<{
  vendor: "chat" | "composer";
}>;

export function RecordProvider({ children, vendor }: RecordProviderProps) {
  return (
    <RecordContext.Provider value={recordStoreMap[vendor]}>
      {children}
    </RecordContext.Provider>
  );
}

const createRecordStore = ({ vendor }: { vendor: RecordStoreVendor }) =>
  createStore<IRecordStoreType>((set, get, store) => ({
    ...createRecordArtifactSlice(set, get, store),
    vendor,
    activeSession: "",
    /**
     * 原子化更新 不附带其他操作
     */
    async updateActiveSessionAndSessionHistory(sessionId: string) {
      if (!sessionId) {
        set({ sessionHistory: undefined, activeSession: "" });
      }
      else {
        set({ activeSession: sessionId });
        const data = await kwaiPilotBridgeAPI.getSession({ sessionId });
        if (data) {
          get().setSessionHistory(data);
        }
      }
    },
    updateQAItem(chatId, updater) {
      set(state =>
        produce(state, (draft) => {
          const item = draft?.sessionHistory?.cachedMessages.find(
            v => v.id === chatId,
          );
          if (item) {
            updater(item);
          }
        }),
      );
    },
    setActiveSession: (param) => {
      const {
        value: activeSession,
        updateLocalStorage = true,
        navigateToChat = true,
        updateHistory = true,
      } = param;
      const loadingStatu = get().loadingStatu;
      const sessionHistory = get().sessionHistory;

      if (updateHistory) {
        if (!activeSession || activeSession === "") {
          set({ sessionHistory: undefined });
        }
        else if (sessionHistory?.sessionId !== activeSession) {
          kwaiPilotBridgeAPI
            .getSession({ sessionId: activeSession })
            .then((data) => {
              if (data) {
                get().setSessionHistory(data);
              }
              else {
                // FIXME: store 内部不要路由跳转
                eventBus.emit("home");
              }
            });
        }
      }

      if (updateLocalStorage) {
        if (vendor === "chat") {
          setSessionToLocal(activeSession);
          kwaiPilotBridgeAPI.updateState(
            WorkspaceState.ACTIVE_SESSION_ID,
            activeSession,
          );
        }
        else if (vendor === "composer") {
          setLocalStorageValue("activeComposerSessionId", activeSession);
          kwaiPilotBridgeAPI.updateState(
            WorkspaceState.ACTIVE_COMPOSER_SESSION_ID,
            activeSession,
          );
        }
      }
      if (loadingStatu && activeSession !== get().activeSession) {
        // 切换会话时，停止接收消息
        get().abortCurrentChat();
        if (sessionHistory) {
          const currentQA = sessionHistory.cachedMessages.find(
            v => v.id === loadingStatu?.id,
          );
          currentQA
          && kwaiPilotBridgeAPI.addMessage({
            item: currentQA,
            sessionId: sessionHistory.sessionId,
            chatId: loadingStatu.id,
          });
        }
        set({ loadingStatu: undefined });
      }

      // TODO: 通信有点复杂了,是否可以直接通过路由参数跳转, 取消 setActiveSession.navigateToChat
      if (navigateToChat) {
        if (vendor === "chat" && (get().sessionHistory || (activeSession !== "" && activeSession))) {
          eventBus.emit("chat");
        }
        else {
          eventBus.emit("home");
        }
      }
      set({ activeSession });
    },
    loadingStatu: undefined,
    finishLoading: () => {
      set({ loadingStatu: undefined });
    },
    setLoadingStatu: (chatId, status) => {
      set({ loadingStatu: { id: chatId, status: status ?? "loading" } });
    },
    getLoadingStatu: () => {
      return get().loadingStatu;
    },
    useSearch: false,
    setUseSearch: (useSearch) => {
      set({ useSearch });
    },
    chatModel: {
      modelType: DEFAULT_MODEL_TYPE as IChatModelType,
    },
    setChatModel: (chatModel) => {
      set({ chatModel });
    },
    setChatModelType: (value) => {
      set({
        chatModel: {
          modelType: value,
        },
      });
    },
    docId: 0,
    setDocId: (docId) => {
      set({ docId });
    },
    dialogSetting: [],
    setDialogSetting: (newSetting, updateLocalStorage = true) => {
      if (updateLocalStorage) {
        updateDialogSetting(newSetting);
      }
      set(state => ({
        dialogSetting: [...state.dialogSetting, newSetting],
      }));
    },
    initSetting: (newSetting) => {
      set({ dialogSetting: newSetting });
    },
    fileList: [],
    setFileList(v) {
      set({ fileList: v });
    },
    clearFileList() {
      set({ fileList: [] });
    },
    removeFile(v) {
      const fileList = get().fileList;
      const newFileList = fileList.filter(item => item.id !== v.id);
      set({ fileList: newFileList });
    },
    clearRecordSetting() {
      const activeSession = genNewActiveSession();
      set({
        useSearch: false,
        docId: 0,
        chatModel: {
          modelType: DEFAULT_MODEL_TYPE,
        },
        fileList: [],
        selectCode: null,
        activeSession: activeSession,
      });
    },
    selectCode: null,
    setSelectCode(v) {
      set({ selectCode: v });
    },
    modelList: [],
    docList: [],
    setModelList: (list) => {
      set({ modelList: list });
    },
    setDocList: (list) => {
      set({ docList: list });
    },
    sessionHistory: undefined,
    setSessionHistory: (
      sessionHistory,
      { checkExpirationStatus = true } = {},
    ) => {
      const lastAsk = new Date(sessionHistory.sessionTime).getTime();
      if (
        checkExpirationStatus
        && Date.now() - lastAsk > ContextExpiredTime
        && !sessionHistory.expiredIndex.includes(
          sessionHistory.cachedMessages.length - 1,
        )
      ) {
        sessionHistory.expiredIndex.push(
          sessionHistory.cachedMessages.length - 1,
        );
        kwaiPilotBridgeAPI.updateSessionInfo({
          expiredIndex: sessionHistory.expiredIndex,
          clearContextIndex: sessionHistory.clearContextIndex,
          sessionId: sessionHistory.sessionId,
        });
      }

      set({ sessionHistory: sessionHistory });

      get().parseMessages(sessionHistory.cachedMessages);

      useComposerStatusStore.getState().updateBySession(sessionHistory);
    },
    clearSessionHistory: () => {
      set({ sessionHistory: undefined });
    },
    summaryConversation: undefined,
    setSummaryConversation: (v) => {
      set({ summaryConversation: v });
    },
    suggestQuestion: [],
    setSuggestQuestion: (v) => {
      set({ suggestQuestion: v ?? [] });
    },
    clearSuggestQuestion: () => {
      set({ suggestQuestion: [] });
    },
    stopReceiveMessageId: "",
    setStopReceiveMessageId: (id) => {
      set({ stopReceiveMessageId: id });
    },
    currentChatAbortController: null,
    abortCurrentChat: () => {
      const controller = get().currentChatAbortController;
      if (controller) {
        controller.abort();
        set({ currentChatAbortController: null });
      }
    },
    setCurrentChatAbortController: (controller) => {
      set({ currentChatAbortController: controller });
    },
    _replyBuffer: {},

    signalMessageDone(chatId) {
      const {
        sessionHistory,
        finishLoading,
        setSuggestQuestion,
        activeSession,
      } = get();

      batchUpdateReply(chatId, store);
      // immediately
      batchUpdateReply.flush();

      finishLoading();
      if (sessionHistory) {
        const index = sessionHistory.cachedMessages.length - 1;
        const lastQA = sessionHistory.cachedMessages[index];
        if (lastQA.id === chatId) {
          kwaiPilotBridgeAPI.addMessage({
            item: lastQA,
            sessionId: activeSession,
            chatId,
          });
          httpClient
            .getSuggestQuestion({
              question: lastQA.Q.question,
              answer: lastQA.A[lastQA.A.length - 1].reply ?? "",
            })
            .then((qs) => {
              setSuggestQuestion(qs);
            });
        }
        else {
          const currentQA = sessionHistory.cachedMessages.find(
            item => item.id === chatId,
          );
          if (currentQA) {
            kwaiPilotBridgeAPI.addMessage({
              item: currentQA,
              sessionId: activeSession,
              chatId,
            });
          }
        }
      }
    },
    addReplyChunk(chatId, { replyContent }) {
      const userInfo = useUserStore.getState().userInfo;
      if (!get().sessionHistory || !userInfo) {
        return;
      }

      set(s => produce(s, (draft) => {
        draft._replyBuffer[chatId] = (draft._replyBuffer[chatId] || "") + replyContent;
      }));

      batchUpdateReply(chatId, store);
    },
  }));
/**
 * 当前 composer record 中改动的文件
 * @returns
 */
export const useComposerAffectedFileList = () => {
  const { mappedByQAItem } = useComposerAffectedFile();
  const fileList = useMemo(
    () => uniqBy(Object.values(mappedByQAItem).flat(), v => v.filepath),
    [mappedByQAItem],
  );
  return {
    fileList,
  };
};

const recordStoreMap = {
  chat: createRecordStore({ vendor: ReocrdStoreVendorEnum.chat }),
  composer: createRecordStore({ vendor: ReocrdStoreVendorEnum.composer }),
};

/**
 * composer 模式下, 代码块要展示 x/y, 用于标识一共有多少文件修改, 当前是第几个.
 * 这部分数据是否要存到 sqlite 还待定
 */
export const useComposerAffectedFile = () => {
  const messages = useRecordStore(
    state => state.sessionHistory?.cachedMessages,
  );

  /* QAItem -> 该 QA 中修改的文件 */
  const mappedByQAItem = useMemo<
    Record<
      /* QAItem[id] */
      string,
      {
        filepath: string;
        filename: string;
      }[]
    >
  >(() => {
    if (!messages) {
      return {};
    }
    return zipObject(
      messages.map(v => v.id),
      messages
        .map(v => v.A.at(-1)?.reply)
        .map(reply => (reply ? findModifiedFilesInMarkdown(reply) : [])),
    );
  }, [messages]);
  return {
    mappedByQAItem,
  };
};

const batchUpdateReply = debounce((id: string, store: StoreApi<IRecordStoreType>) => {
  const recordStoreState = store.getState();
  const userInfo = useUserStore.getState().userInfo;
  const { sessionHistory, stopReceiveMessageId, _replyBuffer } = recordStoreState;
  if (!sessionHistory || !userInfo || !_replyBuffer[id]) {
    return;
  }

  if (stopReceiveMessageId && id === stopReceiveMessageId) {
    return;
  }

  store.setState(s => produce(s, (draft) => {
    draft._replyBuffer[id] = "";
  }));

  const newCachedMessages = produce(sessionHistory.cachedMessages, (draft) => {
    const cachedMessages = draft;
    let QA: QAItem | undefined = cachedMessages.at(-1);

    if (!QA) {
      return;
    }
    if (QA.id !== id) {
      QA = cachedMessages.find(item => item.id === id);
    }

    if (QA) {
      const lastA = QA.A[QA.A.length - 1];
      lastA.reply
              = (lastA.reply || "") + _replyBuffer[id];
    }
  });

  recordStoreState.setSessionHistory({
    ...sessionHistory,
    cachedMessages: newCachedMessages,
  });

  recordStoreState.parseMessages(newCachedMessages);
}, 10);
