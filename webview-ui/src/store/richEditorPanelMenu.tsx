import { create } from "zustand";
import path from "path-browserify";
import {
  CommandType,
  DisableRichEditorMenu,
  SharpCommand,
  SlashCommand,
} from "@shared/types";
import IconBeta from "@/assets/beta.svg?react";
import { RichEditorBoxPanelData, RichEditorBoxPanelDataCustomPrompt, RichEditorMenuType } from "@/components/TextArea/const";
import { RichEditorDisabledReason } from "@shared/constant";
import { CustomPromptData } from "shared/lib/CustomVariable";
import repoChatService from "@/services/repo-chat";
import { URI } from "vscode-uri";
import { isRuleFile } from "shared/lib/util";

export interface RichEditorMenu {
  disabledMenu: DisableRichEditorMenu;
  sharpCommand: RichEditorBoxPanelData[];
  slashCommand: RichEditorBoxPanelData[];
  codeSearchDefaultFiles: RichEditorBoxPanelData[];
  codeSearchDefaultDir: RichEditorBoxPanelData[];
  codeSearchWorkspaceFiles: RichEditorBoxPanelData[];
  codeSearchWorkspaceDir: RichEditorBoxPanelData[];
  customPrompts: RichEditorBoxPanelDataCustomPrompt[];
  ruleFiles: RichEditorBoxPanelData[];
  setDisabledMenu: (params: DisableRichEditorMenu) => void;
  setCodeSearchDefaultFiles: (files: string[]) => void;
  setCodeSearchDefaultDir: (files: string[]) => void;
  setCodeSearchWorkspaceFiles: (files: string[]) => void;
  setCodeSearchWorkspaceDir: (files: string[]) => void;
  updateCurrentFilePath: (files: string) => void;
  updateCodeBasePath: (files: string) => void;
  setCustomPrompts: (prompts: CustomPromptData[]) => void;
  setRuleFiles: (files: string[]) => void;
}

const filterPath = [".", "/"];

export const useRichEditPanelMenuStore = create<RichEditorMenu>((set, get) => {
  return {
    disabledMenu: {
      [SharpCommand.CURRENT_FILE]: {
        status: true,
        msg: RichEditorDisabledReason.unopenedFile,
      },
      [SharpCommand.FOLDER]: {
        status: false,
        msg: "",
      },
      [SharpCommand.FILE]: {
        status: true,
        msg: "",
      },
      [SharpCommand.CODEBASE]: {
        status: false,
        msg: "",
      },
      [SharpCommand.RULES]: {
        status: false,
        msg: "",
      },
      [SlashCommand.LINE_CODE_COMMENT]: {
        status: true,
        msg: "",
      },
      [SlashCommand.CODE_EXPLAIN]: {
        status: true,
        msg: "",
      },
      [SlashCommand.FUNC_COMMENT]: {
        status: true,
        msg: "",
      },
      [SlashCommand.CODE_REFACTOR]: {
        status: true,
        msg: "",
      },
      [SlashCommand.UNIT_TEST]: {
        status: true,
        msg: "",
      },
      [SlashCommand.FUNC_SPLIT]: {
        status: true,
        msg: "",
      },
      [SlashCommand.CLEAR_CONVERSATION]: {
        status: false,
        msg: "",
      },
      [SlashCommand.CUSTOM_PROMPT]: {
        status: false,
        msg: "",
      },
    },
    sharpCommand: [
      {
        key: SharpCommand.CURRENT_FILE,
        title: "当前文件",
        description: "",
        type: "normal",
        data: "",
        commandType: CommandType.SHARP,
        uri: "",
      },
      {
        key: SharpCommand.CODEBASE,
        title: "代码库",
        description: "",
        type: "normal",
        data: "",
        commandType: CommandType.SHARP,
        uri: "",
      },
      {
        key: SharpCommand.FOLDER,
        title: "目录",
        description: "",
        type: "submenu",
        submenu: RichEditorMenuType.FOLDER,
        data: SharpCommand.FOLDER,
        commandType: CommandType.SHARP,
        uri: "",
      },
      {
        key: SharpCommand.FILE,
        title: "文件",
        description: "",
        type: "submenu",
        submenu: RichEditorMenuType.FILE,
        data: SharpCommand.FOLDER,
        commandType: CommandType.SHARP,
        uri: "",
      },
      {
        key: SharpCommand.RULES,
        title: "规则",
        description: "",
        type: "submenu",
        submenu: RichEditorMenuType.RULES,
        data: SharpCommand.RULES,
        commandType: CommandType.SHARP,
        uri: "",
      },
    ],
    slashCommand: [
      {
        title: "函数注释",
        key: SlashCommand.FUNC_COMMENT,
        description: "",
        type: "normal",
        data: "",
        commandType: CommandType.SLASH,
        uri: "",
        search: [
          "函数注释",
          "hanshuzhushi",
          "hszs",
          "zs",
          "s",
          "注释",
          "释",
          "zhushi",
          "数",
        ],
      },
      {
        title: "行间注释",
        key: SlashCommand.LINE_CODE_COMMENT,
        description: "",
        type: "normal",
        data: "",
        commandType: CommandType.SLASH,
        uri: "",
        search: [
          "行间注释",
          "hangjianzhushi",
          "hjzs",
          "zs",
          "s",
          "注释",
          "释",
          "zhushi",
          "间",
          "j",
        ],
      },
      {
        title: "代码解释",
        key: SlashCommand.CODE_EXPLAIN,
        description: "",
        type: "normal",
        data: "",
        commandType: CommandType.SLASH,
        uri: "",
        search: [
          "代码解释",
          "daimajieshi",
          "dmjs",
          "js",
          "s",
          "解释",
          "释",
          "jieshi",
          "码",
          "m",
        ],
      },
      {
        title: "代码优化",
        key: SlashCommand.CODE_REFACTOR,
        description: "",
        type: "normal",
        data: "",
        commandType: CommandType.SLASH,
        uri: "",
        search: [
          "代码优化",
          "daimayouhua",
          "dmyh",
          "yh",
          "h",
          "优化",
          "化",
          "youhua",
          "码",
          "m",
        ],
      },
      {
        title: "函数拆分",
        key: SlashCommand.FUNC_SPLIT,
        description: "",
        type: "normal",
        data: "",
        commandType: CommandType.SLASH,
        uri: "",
        search: [
          "函数拆分",
          "hanshuchaifen",
          "hscf",
          "cf",
          "f",
          "拆分",
          "分",
          "chaifen",
          "数",
          "s",
        ],
      },
      {
        title: "单元测试",
        key: SlashCommand.UNIT_TEST,
        description: "",
        type: "normal",
        data: "",
        commandType: CommandType.SLASH,
        uri: "",
        search: [
          "单元测试",
          "danyuanceshi",
          "dycs",
          "cs",
          "s",
          "测试",
          "试",
          "ceshi",
          "元",
          "y",
        ],
      },
      {
        title: "自定义指令",
        titleSuffix: (
          <span className=" ml-1">
            <IconBeta />
          </span>
        ),
        key: SlashCommand.CUSTOM_PROMPT,
        description: "",
        type: "submenu",
        submenu: RichEditorMenuType.CUSTOM_PROMPT,
        data: "",
        commandType: CommandType.SLASH,
        uri: "",
        search: [
          "自定义指令",
          "zidingyizhiling",
          "zdyzl",
          "zdzl",
          "zl",
          "自定义",
          "zidingyi",
          "zdy",
          "zdy",
          "指令",
          "zhiling",
          "zl",
        ],
      },
      {
        title: "清除上下文",
        key: SlashCommand.CLEAR_CONVERSATION,
        description: "",
        type: "normal",
        data: "",
        commandType: CommandType.SLASH,
        uri: "",
        search: [
          "清除上下文",
          "qingchushangxiawen",
          "qcsxw",
          "sxw",
          "w",
          "上下文",
          "清除",
          "shangxiawen",
          "qing",
          "qingchu",
        ],
      },
    ],
    codeSearchDefaultFiles: [],
    codeSearchDefaultDir: [],
    codeSearchWorkspaceFiles: [],
    codeSearchWorkspaceDir: [],
    customPrompts: [],
    ruleFiles: [],

    setDisabledMenu(params: DisableRichEditorMenu) {
      set({
        disabledMenu: {
          ...get().disabledMenu,
          ...params,
        },
      });
    },
    setCodeSearchDefaultFiles(files: string[]) {
      files = files.filter(f => !filterPath.includes(f) && !isRuleFile(f));
      const data: RichEditorBoxPanelData[] = files.map((file) => {
        return {
          title: path.basename(file),
          description: file,
          key: SharpCommand.FILE,
          type: "normal",
          data: file,
          uri: URI.file(repoChatService.getAbsolutePath(file)).toString(),
          commandType: CommandType.SHARP,
        } satisfies RichEditorBoxPanelData;
      });
      set({
        codeSearchDefaultFiles: data,
      });
    },
    setCodeSearchDefaultDir(dir: string[]) {
      dir = dir.filter(f => !filterPath.includes(f));
      const data: RichEditorBoxPanelData[] = dir.map((file) => {
        return {
          title: path.basename(file),
          description: file,
          key: SharpCommand.FOLDER,
          type: "normal",
          data: file,
          commandType: CommandType.SHARP,
          uri: URI.file(repoChatService.getAbsolutePath(file)).toString(),
        };
      });
      set({
        codeSearchDefaultDir: data,
      });
    },
    setCodeSearchWorkspaceFiles(files: string[]) {
      files = files.filter(f => !filterPath.includes(f));
      const data: RichEditorBoxPanelData[] = files.map((file) => {
        return {
          title: path.basename(file),
          description: file,
          key: SharpCommand.FILE,
          type: "normal",
          data: file,
          commandType: CommandType.SHARP,
          uri: URI.file(repoChatService.getAbsolutePath(file)).toString(),
        };
      });
      set({
        codeSearchWorkspaceFiles: data,
      });
    },
    setCodeSearchWorkspaceDir(dir: string[]) {
      dir = dir.filter(f => !filterPath.includes(f));
      const data: RichEditorBoxPanelData[] = dir.map((file) => {
        return {
          title: path.basename(file),
          description: file,
          key: SharpCommand.FOLDER,
          type: "normal",
          data: file,
          commandType: CommandType.SHARP,
          uri: URI.file(repoChatService.getAbsolutePath(file)).toString(),
        };
      });
      set({
        codeSearchWorkspaceDir: data,
      });
    },

    updateCurrentFilePath(path: string) {
      const sharpCommand = get().sharpCommand;
      const current = sharpCommand.find(
        item => item.key === SharpCommand.CURRENT_FILE,
      );
      if (current) {
        current.data = path;
        set({
          sharpCommand,
        });
      }
    },
    updateCodeBasePath(path: string) {
      const sharpCommand = get().sharpCommand;
      const current = sharpCommand.find(
        item => item.key === SharpCommand.CODEBASE,
      );
      if (current) {
        current.data = path;
        set({
          sharpCommand,
        });
      }
    },
    setCustomPrompts(prompts: CustomPromptData[]) {
      set({
        customPrompts: prompts.map<RichEditorBoxPanelDataCustomPrompt>(i => ({
          key: SlashCommand.CUSTOM_PROMPT,
          title: i.name,
          description: i.content,
          type: "customPrompt",
          data: i.content,
          commandType: CommandType.SLASH,
          raw: i,
          uri: "",
        })),
      });
    },
    setRuleFiles(files: string[]) {
      const data: RichEditorBoxPanelData[] = files.map((file) => {
        return {
          title: path.basename(file),
          description: file,
          key: SharpCommand.RULES,
          type: "normal",
          data: file,
          commandType: CommandType.SHARP,
          uri: URI.file(repoChatService.getAbsolutePath(file)).toString(),
        };
      });
      set({
        ruleFiles: data,
      });
    },
  };
});
