import { IChatModelType } from "../business";

export interface GeneratePromptFile {
  code: string;
  language: string;
  name: string;
}

export interface PushCodeSearchSelectFilesOrDirsParams {
  files: string[];
  dirs: string[];
}

export interface CodeSearchGeneratePromptRequest {
  codebaseSearch: boolean;
  commit: string;
  files: GeneratePromptFile[];
  query: string;
  repoName: string;
  searchTargetDirs: string[];
  username: string;
}

export interface PromptConfig {
  key: string;
  name: string;
  template: string;
}

export interface SlashCommandActiveTextInfo {
  startLine: number;
  endLine: number;
  filename: string;
  language: string;
  code: string;
}

export interface CodeSearchSelectParams {
  file: string[];
  dir: string[];
}

export interface CurrentFilePathAndRepoPath {
  filePath?: string;
  repoPath?: string;
}

export type DisableRichEditorMenu = Record<string, { status: boolean; msg: string }>;

export enum SharpCommand {
  CURRENT_FILE = "kwaipilot-sharp-command-current_file",
  CODEBASE = "kwaipilot-sharp-command-codebase",
  FOLDER = "kwaipilot-sharp-command-folder",
  FILE = "kwaipilot-sharp-command-file",
  RULES = "kwaipilot-sharp-command-rules",
}

export enum SlashCommand {
  LINE_CODE_COMMENT = "kwaipilot-slash-command-line_code_comment",
  CODE_EXPLAIN = "kwaipilot-slash-command-code_explain",
  FUNC_COMMENT = "kwaipilot-slash-command-func_comment",
  CODE_REFACTOR = "kwaipilot-slash-command-code_refactor",
  CLEAR_CONVERSATION = "kwaipilot-slash-command-clear_conversation",
  UNIT_TEST = "kwaipilot-slash-command-unit_test",
  FUNC_SPLIT = "kwaipilot-slash-command-func_split",
  CUSTOM_PROMPT = "kwaipilot-slash-command-custom_prompt",
}

export enum CommandType {
  SHARP = "sharp",
  SLASH = "slash",
}

export enum CommandPrefix {
  SHARP = "kwaipilot-sharp-command-",
  SLASH = "kwaipilot-slash-command-",
}

export interface InlineChatInfo {
  filepath: string;
  startLine: number;
  endLine: number;
  content: string;
  suffix?: string;
  prefix?: string;
}

export type UserInfo = {
  name: string;
  ticket: string;
  mail: string;
  displayName: string;
  avatar: string;
};

export type ReceiveMessageInfo = {
  question: string;
  resType?: "actionResult";
  reply: string;
  id: string;
  isTip: boolean;
  isResend?: boolean;
  section?: any;
  fullPath?: string;
  modelType: IChatModelType;
};

export type CodeSection = {
  type: "class" | "function" | "hoc";
  name: string;
  kind: string;
  offsetStart: number;
  offsetEnd: number;
  lineStart: number;
  lineEnd: number;
  content: string;
  blockStart: number;
  blockEnd: number;
};
export type ActionType =
  | "comment"
  | "test"
  | "functionComment"
  | "lineComment"
  | "codeExplanation"
  | "tuningSuggestion"
  | "functionSplit"
  | "explain"
  | "optimization";
export type CodeActionParams = {
  type: ActionType;
  selectText: string;
  languageId: string;
  section?: CodeSection;
  fullPath?: string;
  param?: any;
};
export type QuickAskParams = {
  type: "comment" | "test" | "explain";
};

export type ChatParam = {
  content: string;
  uniqueId: string;
  chatRecords: any[];
  chatType: any;
  sessionId: string;
  isResend: boolean;
  useSearch: boolean;
  refFiles: number[];
  chatModel: {
    modelType: IChatModelType;
  };
  linkRepoId: string;
  refLinks?: string[];
};

export type FeedBackCommon = {
  question: string; chatId: string; reply: string;
};

export type EditorConfig = {
  fontSize: number;
  fontFamily: string;
  tabSize: number;
  theme: string;
};

export type ChatHistory = {
  role: "user" | "assistant";
  content: string;
};
