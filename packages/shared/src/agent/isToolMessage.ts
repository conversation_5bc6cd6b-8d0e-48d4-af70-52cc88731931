import { InternalLocalMessage, InternalLocalMessage_Tool_EditFile } from "./types";
import { LocalMessage, SayTool } from "./types_copied_from_agent";

export function isToolMessage(message: LocalMessage): message is LocalMessage;
export function isToolMessage(message: InternalLocalMessage): message is InternalLocalMessage;
export function isToolMessage(message: InternalLocalMessage | LocalMessage): boolean {
  return message.type === "say" && message.say === "tool";
}

export function isToolEditFileMessage(message: InternalLocalMessage): message is InternalLocalMessage_Tool_EditFile;
export function isToolEditFileMessage(message: LocalMessage): boolean;
export function isToolEditFileMessage(message: LocalMessage | InternalLocalMessage): boolean {
  if (!isToolMessage(message)) {
    return false;
  }
  if (!message.text) {
    return false;
  }
  try {
    const toolObj = JSON.parse(message.text) as SayTool;
    return toolObj.tool === "editFile";
  }
  catch (e) {
    return false;
  }
}

export function isTerminalMessage(message: LocalMessage | InternalLocalMessage): boolean {
  return message.ask === "command" || message.say === "command";
}

export function isMcpMessage(message: LocalMessage | InternalLocalMessage): boolean {
  return message.ask === "use_mcp_tool" || message.say === "use_mcp_tool_result";
}

export function isCheckpointCreatedMessage(message: LocalMessage | InternalLocalMessage): boolean {
  return message.type === "say" && message.say === "checkpoint_created" && !!message.lastCheckpointHash;
}
