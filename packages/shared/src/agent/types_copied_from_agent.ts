import { MentionNodeV2Structure } from "../MentionNodeV2/nodes";

export type WebviewMessage<
  T extends "newTask" | "askResponse" | "restore" | "stop" = | "newTask"
  | "askResponse"
  | "restore"
  | "stop",
> = T extends "newTask"
  ? {
      type: "newTask";
      taskForLlm: string;
      task: string;
      reqData: ChatRequest;
      localMessages: LocalMessage[];
      contextItems: MentionNodeV2Structure[];
    }
  : T extends "askResponse"
    ? {
        type: "askResponse";
        askResponse: "yesButtonClicked" | "noButtonClicked" | "messageResponse";
        text: string;
      }
    : T extends "restore"
      ? {
          type: "restore";
          params: {
            sessionId: string;
            chatId?: string;
            restoreCommitHash: string;
          };
        }
      : T extends "stop"
        ? {
            type: "stop";
            params: {
              sessionId: string;
              chatId?: string;
            };
          }
        : never;

export interface LocalMessage {
  /** 消息的时间戳(timestamp) */
  ts: number;
  role?: "user";
  sessionId: string;
  chatId?: string;

  /** 消息类型：请求类消息(ask)或者回复类消息(say) */
  type: "ask" | "say";

  /** 当type为"ask"时的具体请求类型 */
  ask?: Ask;

  /** 当type为"say"时的具体响应类型 */
  say?: Say;

  /** 消息的具体文本内容 */
  text?: string;

  /** 是否为部分消息(未完成的消息) */
  partial?: boolean;

  /** 最后检查点的哈希值 */
  lastCheckpointHash?: string;

  /** 检查点是否已签出 */
  isCheckpointCheckedOut?: boolean;

  /** 对话历史索引 */
  conversationHistoryIndex?: number;

  /** 对话历史被截断的范围 */
  conversationHistoryDeletedRange?: [number, number];
}

/**
 * 请求类型的消息种类，需要用户交互的消息
 */
export type Ask =
  /** 跟进问题 */
  | "followup"
  /** 命令执行 */
  | "command"
  /** mcp */
  | "use_mcp_tool"
  /** 命令执行输出 */
  | "command_output"
  /** 工具使用 */
  | "tool"
  /** API请求失败 */
  | "api_req_failed"
  /** 达到错误次数限制 */
  | "mistake_limit_reached";

/**
 * 响应类型的消息种类，纯展示/通知类消息，不需要用户交互
 */
export type Say =
  /** 任务 */
  | "task"
  /** 错误 */
  | "error"
  /** API请求开始 */
  | "api_req_started"
  /** API请求失败 */
  | "api_req_failed"
  /** API请求完成 */
  | "api_req_finished"
  /** 文本 */
  | "text"
  /** 完成结果 */
  | "completion_result"
  /** 用户反馈 */
  | "user_feedback"
  /** 编辑文件的结果 */
  | "edit_file_result"
  /** API请求重试 */
  | "api_req_retried"
  /** 工具错误 */
  | "tool_error"
  /** 命令 */
  | "command"
  /** 命令输出 */
  | "command_output"
  /** mcp工具结果 */
  | "use_mcp_tool_result"
  /** 工具 */
  | "tool"
  /** 检查点创建 */
  | "checkpoint_created";

export interface DeviceInfo {
  deviceId?: string;
  deviceModel?: string;
  deviceName?: string;
  deviceOsName?: string;
  deviceOsVersion?: string;
  ide?: string;
  ideVersion?: string;
  platform?: string;
  pluginVersion?: string;
}

export interface ProjectInfo {
  gitUrl?: string;
  openedFilePath?: string;
  projectName?: string;
}

export interface ChatRequest {
  chatId?: string;
  deviceInfo?: DeviceInfo;
  messages: MessageParam[];
  model?: string;
  projectInfo?: ProjectInfo;
  refFiles?: number[];
  sessionId: string;
  systemPrompt?: string;
  useSearch?: boolean;
  username?: string;
}

export interface ChatResponse {
  message: {
    content: string;
  };
  traceId: string;
}

export interface TextBlockParam {
  text: string;

  type: "text";
}

export type ToolResponse = string | Array<TextBlockParam>;

export interface MessageParam {
  content:
    | string
    | Array<TextBlockParam>;

  role: "user" | "assistant";
  chatId?: string;
}

/*
editFile 参数：
path: 文件路径
content: 文件内容
instructions: 编辑指令
language: 文件语言
*/
/*
readFile 参数：
path: 文件路径
*/
/*
listFilesTopLevel 参数：
path: 文件路径
*/
/*
listFilesRecursive 参数：
path: 文件路径
*/
/*
grepSearch 参数：
path: 文件路径
regex: 正则表达式
file_pattern: 文件模式
*/
/*
codebaseSearch 参数：
query: 查询语句
target_directories: 目标目录
content: 内容
*/
/*
executeCommand 参数：
command: 命令
is_background: 是否后台执行
*/

export interface SayTool {
  tool:
    | "editFile"
    | "searchAndReplace"
    | "writeToFile"
    | "readFile"
    | "listFilesTopLevel"
    | "listFilesRecursive"
    // | "listCodeDefinitionNames"
    // | "searchFiles"
    | "grepSearch"
    | "codebaseSearch"
    | "useMcpTool"
    | "executeCommand";
  path?: string;
  query?: string;
  target_directories?: string;
  diff?: string;
  content?: string;
  regex?: string;
  filePattern?: string;
  instructions?: string;
  language?: string;
  is_background?: boolean;
  startLine?: number;
  endLine?: number;
  shouldReadEntireFile?: boolean;
  // 用于工具版本升级
  tool_version?: "v1" | "v2";
}

export interface EditFileRequest {
  path: string;
  content: string;
  instructions?: string;
  language?: "javascript" | "typescript" | "python" | "java" | "html" | "css" | "scss" | "less" | "json" | "jsonc" | "yaml" | "markdown" | "vue" | "vue-html" | "xml" | "php" | "shellscript" | "tsx" | "jsx" | "sql" | "wasm" | "r" | "ruby" | "rust" | "javascriptreact" | "typescriptreact" | "c" | "csharp" | "plaintext" | "jade" | "dockerfile" | "js" | "ts" | "py" | "yml" | "md" | "default";
  lastCheckpointHash?: string;
  composer?: boolean;
}

export interface WriteFileRequest {
  path: string;
  before: string;
  after: string;
}

export interface EditFileResponse {
  type: "success" | "failed";
  content: string;
  newFile?: boolean;
  noModified?: boolean;
}

export interface ExecuteCommandResponse {
  userRejected: boolean;
  result: string;
  completed?: boolean;
}

export interface DiffSet {
  relativePath: string;
  absolutePath: string;
  before: string;
  after: string;
};
