import { Spread, SerializedLexicalNode } from "lexical";
import { displayLineRange, RangeData } from "../CustomVariable/cody-shared/range";
import { URI } from "vscode-uri";
import { displayPathBasename } from "../CustomVariable/cody-shared/displayPath";

export const MENTION_NODE_V2_TYPE = "mentionNodeV2";

export type MentionNodeV2Structure =
  | MentionNodeV2Structure_File
  | MentionNodeV2Structure_Selection
  | MentionNodeV2Structure_Tree
  | MentionNodeV2Structure_Rule;

export interface MentionNodeV2Structure_Common {
  /**
   * 文件的 uri
   */
  uri: string;

  /**
  * 相对于 workspace 的路径，便于展示
  */
  relativePath: string;
}

export interface MentionNodeV2Structure_File extends MentionNodeV2Structure_Common {
  type: "file";

}
export interface MentionNodeV2Structure_Rule extends MentionNodeV2Structure_Common {
  type: "rule";

}

export interface MentionNodeV2Structure_Selection extends MentionNodeV2Structure_Common {
  type: "selection";

  range: RangeData;

  content: string;
}

export interface MentionNodeV2Structure_Tree extends MentionNodeV2Structure_Common {
  type: "tree";

}

/**
 * lexical 的 序列化节点
 */
export type SerializedMentionNodeV2 = Spread<
  {
    type: typeof MENTION_NODE_V2_TYPE;
    structure: MentionNodeV2Structure;
  },
  SerializedLexicalNode
>;

export function mentionNodeV2DisplayText(structureData: MentionNodeV2Structure): string {
  switch (structureData.type) {
    case "file":
      return `${decodeURIComponent(displayPathBasename(URI.parse(structureData.uri)))}` || "(empty)";
    case "rule":
      return `${decodeURIComponent(displayPathBasename(URI.parse(structureData.uri)))}` || "(empty)";
    case "selection":{
      // A displayed range of `foo.txt:2-4` means "include all of lines 2, 3, and 4", which means the
      // range needs to go to the start (0th character) of line 5. Also, `RangeData` is 0-indexed but
      // display ranges are 1-indexed.
      const rangeText = structureData.range?.start ? `:${displayLineRange(structureData.range)}` : "";
      return `${decodeURIComponent(displayPathBasename(URI.parse(structureData.uri)))}${rangeText}` || "(empty)";
    }
    case "tree":
      return `${decodeURIComponent(displayPathBasename(URI.parse(structureData.uri)))}` || "(empty)";
  }

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  throw new Error(`unrecognized context item type ${contextItem.type}`);
}

export function isMentionNodeV2(node: SerializedLexicalNode): node is SerializedMentionNodeV2 {
  return Boolean(typeof node === "object" && node && "type" in node && node.type === MENTION_NODE_V2_TYPE);
}
