export type McpServer = {
  name: string;
  config: string;
  status: "connected" | "connecting" | "disconnected";
  error?: string;
  tools?: McpTool[];
  disabled?: boolean;
  timeout?: number;
};

export type McpTool = {
  name: string;
  description?: string;
  inputSchema?: object;
};

export type ToggleMcpServerParams = {
  serverName: string;
  disabled: boolean;
};

export type RestartMcpServerParams = {
  serverName: string;
};

export type DeleteMcpServerParams = {
  serverName: string;
};

export type McpServerChangeEventDetail = {
  code: number;
  isError: boolean;
  message: string;
  mcpServers: McpServer[];
};

export type MCPFeaturedServer = {
  serverId?: string;
  serverName?: string;
  serverTagList?: string[];
  serverDescription?: string;
  serverIntroductionMarkdown?: string;
  serverIntroductionLink?: string;
  provider?: string;
  serverIconLink?: string;
  serverType?: string;
  usageScenarioList?: string[];
  publishAt?: number;
  modifiedAt?: number;
  createdAt?: number;
  internalProvide?: boolean;
  serverConfig?: {
    repository?: {
      url?: string;
      type?: string;
    };
    installationArguments?: {
      description?: string;
      key?: string;
      title?: string;
      required?: boolean;
      type?: string;
      placeholder?: string;
      defaultValue?: string;
    }[];
    installationTemplate?: any;
  };
};

export type InstallMcpParams = {
  mcpServers: Record<string, any>;
};

export type FetchMcpDetailByMarketParams = {
  serverId: string; // mcp 市场唯一 id
};
