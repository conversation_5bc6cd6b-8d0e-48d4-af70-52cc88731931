import { SupportedModels } from "../agent/supportedModels";
import { IndexState } from "../bridge/protocol-observable";
import { McpServerChangeEventDetail } from "../mcp/types";
import { SettingPage } from "../customSettingPanel";
import { InlineChatInfo, UserInfo } from "../misc";

export type StateType = "globalState" | "workspaceState" | "config";

export type WatcherOption = { immediate?: boolean };

export const configScope = "kwaipilot";
export const configPrefix = "settings";
export const enum Config {
  /**
   * api 请求代理地址
   * @type string
   * */
  PROXY_URL = `settings.proxy`,

  /**
   * 模型类型
   * @type string
   * */
  MODEL_TYPE = `settings.modelType`,

  /**
   * 启用代码续写
   * @type boolean
   * */
  ENABLE = `settings.enable`,

  /**
   * 启用注释自动续写（默认不启用）
   * @type boolean
   * */
  COMMENT_COMPLETION_ENABLE = `settings.commentCompletionEnable`,

  /**
   * Maximum number of tokens for which suggestions will be displayed
   * @type number
   * */
  MAX_NEW_TOKENS_FOR_CODE_COMPLETION = `settings.maxNewTokensForCodeCompletion`,

  /**
   * 代码补全等待时间(ms)
   * @type number
   * */
  CODE_COMPLETION_DELAY = `settings.codeCompletionDelay`,

  /**
   * 启用行中自动触发代码续写
   * @type boolean
   * */
  ENABLE_MIDLINE_AUTO_COMPLETIONS = `settings.enableMidlineAutoCompletions`,

  /**
   * 启用代码块操作功能
   * @type boolean
   * */
  ENABLE_CODE_BLOCK_ACTION = `settings.enableCodeBlockAction`,

  /**
   * 启用对话式生成代码提示
   * @type boolean
   * */
  SHOW_INLINE_TIP = `settings.showInlineTip`,

  /**
   * 启用代码可优化消息通知
   * @type boolean
   * */
  ENABLE_OPTIMIZATION_NOTIFICATION = `settings.enableOptimizationNotification`,

  /**
   * 启用 MCP
   * @type boolean
   * */
  ENABLE_MCP = `settings.MCP Management`,

  /**
   * 代码可优化消息通知时间间隔(分钟)
   * @type number
   * */
  NOTIFICATION_INTERVAL = `settings.notificationInterval`,

  /**
   * 启用活动消息通知
   * @type boolean
   * */
  ENABLE_TIPS_NOTIFICATION = `settings.enableTipsNotification`,

  /**
   * 启用对话总结
   * @type boolean
   */
  ENABLE_SUMMARY_CONVERSATION = `settings.enableSummaryConversation`,

  /**
   * 启用代码预测
   * @type boolean
   * @default true
   */
  PREDICTION_ENABLE = `settings.enablePrediction`,

  /**
   * 启用自动构建索引
   * @type boolean
   * @default true
   */
  ENABLE_LOCAL_AGENT = `settings.enableLocalAgent`,
  /**
   * 智能体模式偏好
   * @type string
   * @default "intelligent"
   */
  AGENT_PREFERENCE = `settings.agentPreference`,
}

export enum GlobalState {
  /** 用户信息 */
  USER_INFO = "userSsoInfo",

  /** 设备id */
  DEVICE_ID = "deviceId",

  /** inline-chat信息 */
  INLINE_CHAT_INFO = "inlineChatInfo",

  /** 是否已经同步过本地localstorage中的历史记录 */
  OLD_HISTORY = "oldHistory",

  /** 是否处于 preduction 过程 */
  ON_PREDICTION = "onPrediction",

  /** 是否处于 composer 过程 */
  ON_COMPOSER = "onComposer",

  /** 是否处于 inline chat 过程 */
  ON_INLINE_CHAT = "onInlineChat",

  /** 是否处于开发者模式 */
  IS_DEVELOPER_MODE = "isDeveloperMode",

  /** 助理模式，用户偏好模型 */
  COMPOSER_PREFERRED_MODEL = "composerPreferredModel",
  /**
   * 最大索引空间大小（单位：GB）
   * @type number
   * @default 10
   */
  MAX_INDEX_SPACE = `maxIndexSpace`,
  /** MCP 服务器列表 */
  MCP_SERVERS = "mcpServers",
}
export enum WorkspaceState {
  /** 选择过的文件 */
  CODE_SEARCH_SELECT_FILE_HISTORY = "codeSearchSelectFileHistory",
  /** 选择过的目录目录 */
  CODE_SEARCH_SELECT_DIR_HISTORY = "codeSearchSelectDirHistory",
  /** 打开的文件 and 打开过的文件 */
  CODE_SEARCH_SELECT_OPEN_FILE_HISTORY = "codeSearchSelectOpenFileHistory",
  /** 打开文件的目录 and 打开过文件的目录 */
  CODE_SEARCH_SELECT_OPEN_DIR_HISTORY = "codeSearchSelectOpenDirHistory",
  /** 当前工作区激活的会话 */
  ACTIVE_SESSION_ID = "activeSessionId",
  /* 当前工作区激活的助理(composer) */
  ACTIVE_COMPOSER_SESSION_ID = "activeComposerSessionId",

  /** 索引构建的状态 */
  INDEX_STATE = "indexState",
  /** 自定义设置页面的路由 */
  ACTIVE_SETTING_PAGE = "activeSettingPage",
  /** Rule文件列表 */
  RULE_FILE_LIST = "ruleFileList",
}

export type StateReturnType = {
  config: {
    [Config.PROXY_URL]: string;
    [Config.MODEL_TYPE]: string;
    [Config.ENABLE]: boolean;
    [Config.COMMENT_COMPLETION_ENABLE]: boolean;
    [Config.MAX_NEW_TOKENS_FOR_CODE_COMPLETION]: number;
    [Config.CODE_COMPLETION_DELAY]: number;
    [Config.ENABLE_MIDLINE_AUTO_COMPLETIONS]: boolean;
    [Config.ENABLE_CODE_BLOCK_ACTION]: boolean;
    [Config.SHOW_INLINE_TIP]: boolean;
    [Config.ENABLE_OPTIMIZATION_NOTIFICATION]: boolean;
    [Config.NOTIFICATION_INTERVAL]: number;
    [Config.ENABLE_TIPS_NOTIFICATION]: boolean;
    [Config.ENABLE_SUMMARY_CONVERSATION]: boolean;
    [Config.PREDICTION_ENABLE]: boolean;
    [Config.ENABLE_LOCAL_AGENT]: boolean;
    [Config.AGENT_PREFERENCE]: string;
    [Config.ENABLE_MCP]: boolean;
  };
  globalState: {
    [GlobalState.USER_INFO]: UserInfo | undefined;
    [GlobalState.DEVICE_ID]: string | undefined;
    [GlobalState.INLINE_CHAT_INFO]: InlineChatInfo | undefined;
    [GlobalState.OLD_HISTORY]: boolean;
    [GlobalState.ON_PREDICTION]: boolean;
    [GlobalState.ON_COMPOSER]: boolean;
    [GlobalState.ON_INLINE_CHAT]: boolean;
    [GlobalState.IS_DEVELOPER_MODE]: boolean;
    [GlobalState.COMPOSER_PREFERRED_MODEL]: SupportedModels;
    [GlobalState.MAX_INDEX_SPACE]: number;
    [GlobalState.MCP_SERVERS]: McpServerChangeEventDetail;
  };
  workspaceState: {
    [WorkspaceState.CODE_SEARCH_SELECT_FILE_HISTORY]: string[];
    [WorkspaceState.CODE_SEARCH_SELECT_DIR_HISTORY]: string[];
    [WorkspaceState.CODE_SEARCH_SELECT_OPEN_DIR_HISTORY]: string[];
    [WorkspaceState.CODE_SEARCH_SELECT_OPEN_FILE_HISTORY]: string[];
    [WorkspaceState.ACTIVE_SESSION_ID]: string;
    [WorkspaceState.ACTIVE_COMPOSER_SESSION_ID]: string;
    [WorkspaceState.INDEX_STATE]: IndexState;
    [WorkspaceState.ACTIVE_SETTING_PAGE]: SettingPage;
    [WorkspaceState.RULE_FILE_LIST]: string[];
  };
};
