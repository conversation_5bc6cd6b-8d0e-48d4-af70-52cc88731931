{"name": "shared", "version": "1.0.0", "description": "", "main": "lib/index.js", "types": "lib/index.d.ts", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc -p tsconfig.lib.json --composite false"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@tsconfig/recommended": "^1.0.8", "@types/node": "^12.20.55", "fast-deep-equal": "^3.1.3", "rxjs": "^7.8.1", "typescript": "^5.5.4", "vitest": "^3.0.2"}, "dependencies": {"vscode-uri": "^3.1.0"}}