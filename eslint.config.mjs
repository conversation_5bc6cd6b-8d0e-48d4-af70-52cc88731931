import { fixupConfigRules } from "@eslint/compat";
import js from "@eslint/js";
import tseslint from "typescript-eslint";
import globals from "globals";
import importPlugin from "eslint-plugin-import";
import reactHooks from "eslint-plugin-react-hooks";
import react from "eslint-plugin-react";
import pluginStylistic from "@stylistic/eslint-plugin";
import noDirectBodyOperation from "./eslint-rules/no-direct-body-operation.mjs";
import noDangerousHTML from "./eslint-rules/no-dangerous-html.mjs";
import noDirectChakraImport from "./eslint-rules/no-direct-chakra-import.mjs";
import noDirectIconifyImport from "./eslint-rules/no-direct-iconify-import.mjs";
import noDirectKidImport from "./eslint-rules/no-direct-kid-import.mjs";
import noNavigatorClipboard from "./eslint-rules/no-navigator-clipboard.mjs";
import noDirectDocumentOperation from "./eslint-rules/no-direct-document-operation.mjs";
import mustAliasImport from "./eslint-rules/must-alias-import.mjs";
import noTextCommonPrimaryClass from "./eslint-rules/no-text-common-primary-class.mjs";

// 新增导入
const stylistic = pluginStylistic.configs.customize({
  flat: true,
  indent: 2,
  jsx: true,
  pluginName: "style",
  quotes: "double",
  semi: true,
});
/**
 * 从旧的 eslintrc 迁移过来
 * https://git.corp.kuaishou.com/web-infra/ai-devops/kwaipilot-vscode-extension/-/blob/eb26e5efcdbad8bdb41539bf6b6b2d16ec4aef03/webview-ui/.eslintrc
 * diff:
 * 取消 no-relative-parent-imports, 之前配置有问题, 导致通过 @/xx 可以避开这个检查
 * @/bridge 不规范, 但@bridge 是规范的 未来也可以规范一下
 * 取消 no-extraneous-dependencies 之前配置有问题, 导致实际是不生效的
 * https://github.com/import-js/eslint-plugin-import/issues/496
 * prettier 改为 eslint stylistic, 解决单行代码增加自动换行产生的大量 diff
 */

export default tseslint.config(
  {
    extends: [
      js.configs.recommended,
      ...tseslint.configs.recommended,
    ],
    files: ["**/*.{ts,tsx}"],
    plugins: {
      "react-hooks": reactHooks,
      react,
      "kwaipilot-code-suggestion": {
        rules: {
          "no-direct-body-operation": noDirectBodyOperation,
          "no-dangerous-html": noDangerousHTML,
          "no-direct-chakra-import": noDirectChakraImport,
          "no-direct-iconify-import": noDirectIconifyImport,
          "no-direct-kid-import": noDirectKidImport,
          "no-navigator-clipboard": noNavigatorClipboard,
          "no-direct-document-operation": noDirectDocumentOperation,
          "must-alias-import": mustAliasImport,
          "no-text-common-primary-class": noTextCommonPrimaryClass, // 新增规则
        },
      },
    },
    languageOptions: {
      ecmaVersion: "latest",
      globals: globals.browser,
      sourceType: "module",
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
    rules: {
      ...reactHooks.configs.recommended.rules,
      "kwaipilot-code-suggestion/no-direct-body-operation": "error",
      "kwaipilot-code-suggestion/no-direct-chakra-import": "error",
      "kwaipilot-code-suggestion/no-dangerous-html": "error",
      "kwaipilot-code-suggestion/no-direct-iconify-import": "error",
      "kwaipilot-code-suggestion/no-direct-kid-import": "error",
      "kwaipilot-code-suggestion/no-navigator-clipboard": "error",
      "kwaipilot-code-suggestion/no-direct-document-operation": "error",
      "kwaipilot-code-suggestion/no-text-common-primary-class": "warn", // 新增规则
      "@typescript-eslint/no-explicit-any": "off",
      // non-null-assertion 存量代码太多，先降级为 warn
      "@typescript-eslint/no-non-null-assertion": "warn",
      "@typescript-eslint/no-unused-expressions": [
        "error",
        { allowShortCircuit: true, allowTernary: true },
      ],
      "@typescript-eslint/no-unused-vars": [
        "error",
        // TODO: v9 默认是 all 是否升级一下
        {
          caughtErrors: "none",
          args: "all",
          argsIgnorePattern: "^_",
        },
      ],

      "react-hooks/rules-of-hooks": "error",
      // 这条很重要!
      "react-hooks/exhaustive-deps": "error",
    },
  },
  importPlugin.flatConfigs.recommended,
  fixupConfigRules(importPlugin.configs.typescript),
  {
    rules: {
      "import/no-duplicates": "error",
      "import/no-self-import": "error",
      "import/no-relative-packages": "error",
      "import/no-relative-parent-imports": "off",
      "import/consistent-type-specifier-style": ["error", "prefer-inline"],
      "import/no-empty-named-blocks": "error",
      "import/no-extraneous-dependencies": "off",
      "import/no-import-module-exports": "error",
      "import/newline-after-import": "error",
      "import/exports-last": "off",
      "import/no-useless-path-segments": [
        "error",
        {
          noUselessIndex: true,
        },
      ],
    },
  },
  {
    settings: {
      "import/resolver": {
        typescript: {
          alwaysTryTypes: true, // always try to resolve types under `<root>@types` directory even it doesn't contain any source code, like `@types/unist`

          // use an array
          project: ["webview-ui/tsconfig.json", "tsconfig.json"],
        },
      },
    },
  },
  /* eslintPluginPrettierRecommended,
  {
    rules: {
      "prettier/prettier": [
        "warn",
        {
          endOfLine: "lf",
          singleQuote: false,
        },
      ],
    },
  }, */
  {
    plugins: {
      style: pluginStylistic,
    },
    rules: {
      ...stylistic.rules,
    },
    files: [
      "**/*.?([cm])ts",
      "**/*.?([cm])tsx",
      "**/*.?([cm])js",
      "**/*.?([cm])jsx",
    ],
  },
  {
    rules: {
      "no-restricted-imports": [
        "error",
        {
          patterns: [
            {
              // TODO: 限制引用打包后的产物，但最好通过package json 的 exports 配置来限制
              group: ["shared/src/*"],
              message: "导入路径错误：请从 shared/lib/* 导入，而不是 shared/src/*",
            },
          ],
        },
      ],
    },
  },
);
