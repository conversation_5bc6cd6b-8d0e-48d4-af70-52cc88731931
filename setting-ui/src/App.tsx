import { useCallback, useEffect, useState } from "react";
import "./global.css";
import { FileIndex } from "./pages/fileIndex";
import { MCP } from "./pages/mcp";
import { Rules } from "./pages/rules";
import TipBlock from "./components/TipBlock";
import { kwaiPilotBridgeAPI } from "./bridge";

import { SettingPage } from "shared/lib/customSettingPanel/index";
import { ConfigProvider } from "antd";

declare global {
  interface Window {
    ide?: "vscode";
    vscMediaUrl: string;
    colorThemeName: "dark" | "light";
    webkit?: any;
    kwaipilotBridgeCall?: any;
    WKWebViewJavascriptBridge?: any;
    WKWVJBCallbacks?: any;
    bridge?: any;
    proxyUrl?: string;
    uriQuery?: string;
    // userInfo?: UserInfo;
  }
}

const pageMap: Record<SettingPage, () => JSX.Element> = {
  fileIndex: () => <FileIndex />,
  mcp: () => <MCP />,
  rules: () => <Rules />,
};

const App = () => {
  const [activeTab, setActiveTab] = useState<SettingPage>("fileIndex");

  const handleTabClick = (tabId: SettingPage) => {
    setActiveTab(tabId);
  };

  const updateTheme = (theme: "dark" | "light") => {
    if (theme === "dark") {
      document.body.classList.add("dark");
    }
    else {
      document.body.classList.remove("dark");
    }
  };

  useEffect(() => {
    const subscription = kwaiPilotBridgeAPI.observableAPI.currentTheme().subscribe((theme) => {
      updateTheme(theme);
    });
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  useEffect(() => {
    const subscription = kwaiPilotBridgeAPI.observableAPI.customPanelPage().subscribe((value) => {
      setActiveTab(value);
    });
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const reportPageShowEvent = useCallback(() => {
    switch (activeTab) {
      case "rules":
        kwaiPilotBridgeAPI.extensionWeblogger.$reportUserAction({
          key: "rules_page_show",
        });
        break;
      case "mcp":
        kwaiPilotBridgeAPI.extensionWeblogger.$reportUserAction({
          key: "mcp_page_show",
        });
        break;
      case "fileIndex":
        kwaiPilotBridgeAPI.extensionWeblogger.$reportUserAction({
          key: "file_index_page_show",
        });
        break;
    }
  }, [activeTab]);

  useEffect(() => {
    reportPageShowEvent();
  }, [activeTab, reportPageShowEvent]);

  // 渲染左侧标签头
  const renderTabHeaders = () => {
    return (
      <div className="flex flex-col w-full h-full overflow-y-auto gap-2">
        <div
          className={`py-2 px-4 cursor-pointer rounded-sm ${
            activeTab === "fileIndex"
              ? "bg-[var(--vscode-tab-inactiveBackground)] text-[var(--vscode-foreground)]"
              : ""
          }`}
          onClick={() => handleTabClick("fileIndex")}
        >
          代码索引
        </div>
        <div
          className={`py-2 px-4 cursor-pointer rounded-sm ${
            activeTab === "mcp"
              ? "bg-[var(--vscode-tab-inactiveBackground)] text-[var(--vscode-foreground)]"
              : ""
          }`}
          onClick={() => handleTabClick("mcp")}
        >
          MCP
        </div>
        <div
          className={`py-2 px-4 cursor-pointer rounded-sm ${
            activeTab === "rules"
              ? "bg-[var(--vscode-tab-inactiveBackground)] text-[var(--vscode-foreground)]"
              : ""
          }`}
          onClick={() => handleTabClick("rules")}
        >
          规则配置
        </div>
      </div>
    );
  };

  return (
    <div className="h-[100vh] w-[100vw] flex flex-col">
      <TipBlock />
      <div className="flex flex-1 pt-[24px] pl-[32px] pr-[52px] pb-[32px] gap-4 overflow-hidden">
        <div className="w-48 h-full">
          {renderTabHeaders()}
        </div>
        <div className="w-[1px] bg-[var(--vscode-panel-border)]"></div>
        <ConfigProvider theme={{
          components: {
            Tooltip: {
              fontSize: 13,
              fontWeightStrong: 400,
            },
          },
        }}
        >
          <div className="flex-1 h-full overflow-y-auto">
            {pageMap[activeTab]()}
          </div>
        </ConfigProvider>
      </div>
    </div>
  );
};

export default App;
