{"name": "setting-ui", "version": "1.0.2", "private": true, "scripts": {"dev": "tsc --build & VITE_CJS_IGNORE_WARNING=true vite dev", "start": "tsc --build --watch & vite build -w", "build": "tsc --build && vite build", "build:export": "tsc --build && vite build --outDir ../../../../extensions/kwaipilot/setting-ui/build", "type-check": "tsc --build && tsc --noEmit", "preview": "vite preview"}, "dependencies": {"@infra-node/tee": "^0.4.0", "@ks-radar/radar": "^1.2.12", "@ks/weblogger": "^3.10.35", "@kwaipilot/markdown-render": "^0.0.4", "@lexical/react": "^0.17.1", "@lexical/utils": "^0.17.1", "@lezer/highlight": "^1.2.1", "@tiptap/core": "^2.4.0", "@tiptap/extension-document": "^2.4.0", "@tiptap/extension-paragraph": "^2.4.0", "@tiptap/extension-placeholder": "^2.4.0", "@tiptap/extension-text": "^2.4.0", "@tiptap/pm": "^2.4.0", "@tiptap/react": "^2.4.0", "@tiptap/starter-kit": "^2.4.0", "@tiptap/suggestion": "^2.4.0", "@types/lodash-es": "^4.17.12", "@types/path-browserify": "^1.0.2", "@types/unist": "^3.0.3", "@types/uuid": "^9.0.7", "@vscode-elements/react-elements": "^1.15.0", "@yoda/bridge": "^2.0.12", "antd": "^5.20.3", "clsx": "^2.0.0", "dayjs": "^1.11.13", "framer-motion": "^10.16.12", "html-to-image": "^1.11.11", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "mac-scrollbar": "^0.13.6", "path-browserify": "^1.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-intersection-observer": "^9.13.0", "react-router-dom": "^6.26.0", "react-tooltip": "^5.28.1", "use-immer": "^0.11.0", "uuid": "^9.0.1", "zustand": "^4.4.7"}, "devDependencies": {"@iconify/react": "^5.2.0", "@kid/enterprise-icon": "^1.0.543", "@ks-kwaipilot/artifact-message-parser": "workspace:*", "@tailwindcss/typography": "^0.5.10", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/mdast": "^4.0.4", "@types/nunjucks": "^3.2.6", "@types/react": "^18.2.41", "@types/react-dom": "^18.2.17", "@types/react-router-dom": "^5.3.3", "@types/react-syntax-highlighter": "^15.5.11", "@types/statuses": "^2.0.5", "@types/vscode-webview": "^1.57.4", "@udecode/cn": "^40.2.8", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.16", "immer": "^10.1.1", "less": "^4.2.0", "nunjucks": "^3.2.4", "prettier": "^2.8.8", "react-markdown": "9.0.1", "react-remark": "^2.1.0", "react-use": "^17.6.0", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.0", "remark-parse": "^11.0.0", "shared": "workspace:*", "shiki": "^1.24.0", "statuses": "^2.0.1", "tailwind-merge": "^3.0.1", "tailwindcss": "^3.3.5", "tailwindcss-themer": "^4.0.0", "typescript": "^5.5.4", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "use-resize-observer": "^9.1.0", "vite": "^5.3.4", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.3.2", "vscode-uri": "^3.1.0"}}