import { WorkspaceConfiguration, Memento, workspace } from "vscode";

import { BaseModule } from "..";
import { ContextManager } from "../context-manager";
import { Config, StateReturnType, configScope } from "./types";
import { LoggerManager } from "../logger";
import { DefaultBaseUrl } from "../../const";
import fetch from "node-fetch";
import { ExtensionConfigShape } from "shared/lib/bridge/protocol";

type WatchOption = {
  immediate?: boolean;
};

class StateManager<T extends keyof StateReturnType> extends BaseModule {
  private state: WorkspaceConfiguration | Memento;
  private watcherMap: Map<string, Set<() => any>> = new Map();

  constructor(ext: ContextManager, type: T) {
    super(ext);
    this.state = type === "config" ? workspace.getConfiguration(configScope) : type === "globalState" ? this.context.globalState : this.context.workspaceState;
  }

  get<K extends keyof StateReturnType[T]>(key: K, defaultValue: StateReturnType[T][K]): StateReturnType[T][K];
  get<K extends keyof StateReturnType[T]>(key: K): StateReturnType[T][K] | undefined;
  get<K extends keyof StateReturnType[T]>(key: K, defaultValue?: StateReturnType[T][K]): StateReturnType[T][K] | undefined {
    if (typeof key !== "string") {
      this.logger.error(`get value error`, "state-manager", { reason: `not a vaild key: ${key.toString()}` });
      throw Error(`getStateError key-${key.toString()} is not vaild key`);
    }
    if (key === Config.PROXY_URL) {
      return this.state.get(key) === "" ? (DefaultBaseUrl as any) : this.state.get(key) === undefined ? defaultValue : this.state.get(key);
    }
    return this.state.get(key) === undefined ? defaultValue : this.state.get(key);
  }

  async update<K extends keyof StateReturnType[T]>(key: K, value: StateReturnType[T][K]) {
    if (typeof key !== "string") {
      return;
    }
    const oldVal = this.state.get(key);
    if (JSON.stringify(oldVal) === JSON.stringify(value)) {
      return;
    }
    await this.state.update(key, value);
    this.emit(key, value, oldVal);
  }

  watch<K extends keyof StateReturnType[T]>(
    key: K,
    cb: (val?: StateReturnType[T][K], oldVal?: StateReturnType[T][K]) => void,
    opt: WatchOption = {},
  ) {
    if (typeof key !== "string") {
      this.logger.error(`watch error`, "state-manager", { reason: `not a vaild key: ${key.toString()}` });
      throw Error(`getStateError key-${key.toString()} is not support key`);
    }

    this.removeListener(key as string, cb);

    this.on(key as string, cb);

    if (!this.watcherMap.has(key as string)) {
      this.watcherMap.set(key as string, new Set());
    }
    this.watcherMap.get(key as string)?.add(cb);

    if (opt?.immediate) {
      cb(this.get(key));
    }

    return () => {
      this.removeListener(key as string, cb);
      this.watcherMap.get(key as string)?.delete(cb);
    };
  }

  get logger() {
    return this.getBase(LoggerManager);
  }
}

export class GlobalStateManager extends StateManager<"globalState"> implements ExtensionConfigShape {
  constructor(ext: ContextManager) {
    super(ext, "globalState");
    this.setMaxListeners(20);
  }
}
export class WorkspaceStateManager extends StateManager<"workspaceState"> implements ExtensionConfigShape {
  constructor(ext: ContextManager) {
    super(ext, "workspaceState");
  }
}
export class ConfigManager extends StateManager<"config"> {
  constructor(ext: ContextManager) {
    super(ext, "config");

    // 初始化时检查一次 PROXY_URL
    this.initProxyUrl();

    workspace.onDidChangeConfiguration((e) => {
      if (e.affectsConfiguration(configScope)) {
        const config = workspace.getConfiguration(configScope);
        Object.entries(config.settings).forEach(([key, value]) => {
          const fullKey = `settings.${key}`;
          this.emit(fullKey, value);
        });
      }
    });
  }

  private async initProxyUrl() {
    // const config = workspace.getConfiguration(configScope);
    // const currentUrl = config.get(Config.PROXY_URL);
    try {
      const response = await fetch(DefaultBaseUrl);
      const data = await response.json();
      if (data.error_code === 40314) {
        await this.update(Config.PROXY_URL, "https://kinsight.corp.kuaishou.com");
        return;
      }
    }
    catch (error: any) {
      this.logger.warn(`Failed to check DefaultBaseUrl: ${DefaultBaseUrl} ${error.message}`, "config-manager", { err: error });
    }
  }

  get logger() {
    return this.getBase(LoggerManager);
  }
}
