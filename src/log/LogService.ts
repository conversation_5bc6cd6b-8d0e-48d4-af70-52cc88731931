import axios from "axios";
import {
  CodeCompletionLogParam,
  CompletionLogParam,
  FeedbackLogParam,
  LogParam,
} from "./Model";
import { getConfig } from "../utils/config";

const config = getConfig();

const url
  = "https://log-sdk.ksapisrv.com/rest/wd/common/log/collect/misc2?v=3.10.34kpn=data_kwaipilot";
const completionUrl = `${config.settings.proxyUrl}/eapi/kwaipilot/log/completions`;
const codeCompletionUrl = `${config.settings.proxyUrl}/eapi/kwaipilot/log/code/completions`;
const feedbackLogUrl = `${config.settings.proxyUrl}/eapi/kwaipilot/qa/feedback`;

/** @deprecated 可使用 chatProvider.sendNodeClick  */
export async function collectLog(param: LogParam): Promise<any> {
  let promise;
  try {
    promise = new Promise((resolve, reject) => {
      axios({
        method: "post",
        url: url,
        data: param,
      })
        .then((response) => {
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        });
    });
  }
  catch (error) {
    console.log(error);
  }
  return promise;
}

/** @deprecated 可使用 chatProvider.sendNodeClick  */
export async function collectPageLog(param: {
  deviceId: string;
  userId: string;
  deviceName: string;
  deviceOsVersion: string;
  deviceModel: string;
  page: string;
}): Promise<any> {
  let promise;
  try {
    const {
      deviceId,
      userId,
      deviceName,
      deviceOsVersion,
      deviceModel,
      page,
    } = param;
    // console.log("开始发送 page 埋点:" + page);
    promise = new Promise((resolve) => {
      axios({
        method: "post",
        url: url,
        data: {
          common: {
            identity_package: {
              device_id: deviceId,
              global_id: "",
              user_id: userId,
            },
            app_package: {
              language: "en-GB",
              platform: 10,
              container: "WEB",
              product_name: "data_kwaipilot",
            },
            device_package: {
              os_version: deviceOsVersion,
              model: deviceModel,
              ua: deviceName,
            },
            need_encrypt: false,
            network_package: { type: 3 },
            h5_extra_attr:
              "{\"sdk_name\":\"webLogger\",\"sdk_version\":\"3.10.34\",\"sdk_bundle\":\"log.browser.js\",\"app_version_name\":\"\",\"host_product\":\"\",\"resolution\":\"3584x2240\",\"screen_width\":1792,\"screen_height\":1120,\"device_pixel_ratio\":2,\"domain\":\"https://kinsight.corp.kuaishou.com/\",\"fromIframe\":false}",
            global_attr: "{}",
          },
          logs: [
            {
              client_timestamp: +new Date(),
              client_increment_id: 2,
              session_id: deviceId,
              time_zone: "GMT+08:00",
              event_package: {
                show_event: {
                  action: 2,
                  sub_action: 2,
                  type: 10,
                  first_load: false,
                  time_cost: 0,
                  stay_length: 135677,
                  status: 1,
                  action_type: 1,
                  url_package: {
                    page: page,
                    identity: deviceId + "page",
                    page_type: 2,
                  },
                  refer_url_package: {
                    page: "VS_HOME",
                    identity: deviceId + "refer",
                    page_type: 2,
                  },
                },
              },
            },
          ],
        },
      })
        .then((response) => {
          // console.log("page 埋点请求成功");
          resolve(response);
        })
        .catch((err) => {
          console.log("page 埋点请求失败");
          console.error(err);
        });
    });
  }
  catch (error) {
    console.log(error);
  }
  return promise;
}

/** @deprecated 可使用 chatProvider.sendNodeClick  */
export async function collectClickLog(param: {
  deviceId: string;
  userId: string;
  deviceName: string;
  deviceOsVersion: string;
  deviceModel: string;
  action: string;
}): Promise<any> {
  let promise;
  try {
    const {
      deviceId,
      userId,
      deviceName,
      deviceOsVersion,
      deviceModel,
      action,
    } = param;
    // console.log("开始发送埋点:" + action);
    promise = new Promise((resolve) => {
      axios({
        method: "post",
        url: url,
        data: {
          common: {
            identity_package: {
              device_id: deviceId,
              global_id: "",
              user_id: userId,
            },
            app_package: {
              language: "en-GB",
              platform: 10,
              container: "WEB",
              product_name: "data_kwaipilot",
            },
            device_package: {
              os_version: deviceOsVersion,
              model: deviceModel,
              ua: deviceName,
            },
            need_encrypt: false,
            network_package: { type: 3 },
            h5_extra_attr:
              "{\"sdk_name\":\"webLogger\",\"sdk_version\":\"3.10.34\",\"sdk_bundle\":\"log.browser.js\",\"app_version_name\":\"\",\"host_product\":\"\",\"resolution\":\"3584x2240\",\"screen_width\":1792,\"screen_height\":1120,\"device_pixel_ratio\":2,\"domain\":\"https://kinsight.corp.kuaishou.com\",\"fromIframe\":false}",
            global_attr: "{}",
          },
          logs: [
            {
              client_timestamp: +new Date(),
              client_increment_id: 2318,
              session_id: deviceId,
              time_zone: "GMT+08:00",
              event_package: {
                task_event: {
                  type: 1,
                  status: 0,
                  operation_type: 1,
                  operation_direction: 0,
                  session_id: deviceId,
                  url_package: {
                    page: "VS_HOME",
                    identity: deviceId + "page",
                    page_type: 2,
                  },
                  refer_url_package: {
                    page: "VS_HOME",
                    identity: deviceId + "refer",
                    page_type: 2,
                  },
                  element_package: {
                    action: action,
                  },
                },
              },
            },
          ],
        },
      })
        .then((response) => {
          // console.log("埋点请求成功");
          resolve(response);
        })
        .catch((err) => {
          console.log("埋点请求失败");
          console.error(err);
        });
    });
  }
  catch (error) {
    console.log(error);
  }
  return promise;
}

export async function completionLog(param: CompletionLogParam): Promise<any> {
  let promise;
  try {
    promise = new Promise((resolve, reject) => {
      axios({
        method: "post",
        url: completionUrl,
        data: param,
      })
        .then((response) => {
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        });
    });
  }
  catch (error) {
    console.log(error);
  }
  return promise;
}

/** 代码续写埋点 - V2 */
export async function codeCompletionLog(
  param: CodeCompletionLogParam,
): Promise<any> {
  let promise;
  // console.log("CodeContinuation: ", param);
  try {
    promise = new Promise((resolve, reject) => {
      axios({
        method: "post",
        url: codeCompletionUrl,
        data: param,
      })
        .then((response) => {
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        });
    });
  }
  catch (error) {
    console.log(error);
  }
  return promise;
}

/** 点赞埋点-V2 */
export async function feedbackLog(param: FeedbackLogParam): Promise<any> {
  let promise;

  try {
    promise = new Promise((resolve, reject) => {
      axios({
        method: "post",
        url: feedbackLogUrl,
        data: param.detail,
      })
        .then((response) => {
          resolve(response);
          // console.log(response);
        })
        .catch((err) => {
          reject(err);
        });
    });
  }
  catch (error) {
    console.log(error);
  }
  return promise;
}
