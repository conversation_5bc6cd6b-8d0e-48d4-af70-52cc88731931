import { networkInterfaces } from "os";
import * as CryptoJS from "crypto-js";
import { getConfig } from "./config";
import path from "path";
import fs from "fs";
import { workspace } from "vscode";

import { Logger } from "./log";

const logger = new Logger("utils");

/**
 * 获取指定范围内的随机数
 * @param Min 随机最小值
 * @param Max 随机最大值
 * @returns 随机数
 */
export function getRandomNum(Min: number, Max: number): number {
  const Range = Max - Min;
  const Rand = Math.random();
  return (Min + Math.round(Rand * Range));
}

/**
 * 睡眠
 * @param ms 毫秒数
 * @returns
 */
export function Sleep(ms: number | undefined) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 获取网络地址
export function getNetworkId() {
  const netDict = networkInterfaces();
  for (const devName in netDict) {
    let netList = netDict[devName];
    if (!netList) {
      netList = [];
    }

    for (let i = 0; i < netList.length; i++) {
      const { address, family, internal, mac } = netList[i];
      const isvm: boolean = isVmNetwork(mac);
      if (family === "IPv4" && address !== "127.0.0.1" && !internal && !isvm) {
        return address;
      }
    }
  }
}
export function getSSOUrl(deviceId: string) {
  const { settings } = getConfig();
  const SSO_URL = "https://sso.corp.kuaishou.com/cas/login?service=";
  const service = settings.proxyUrl.replace("https", "http") + "/eapi/kwaipilot/auth/sso/callback?uuid=" + deviceId + "&redirect=";
  const encodeParam = encodeURIComponent(service) + "/";
  return SSO_URL + encodeParam;
}

// 增加一个判断VM虚拟机的方法
// 在上面方法的if中加上这个方法的返回判断就行了
function isVmNetwork(mac: string): boolean {
  // 常见的虚拟网卡MAC地址和厂商
  const vmNetwork = [
    "00:05:69", // vmware1
    "00:0C:29", // vmware2
    "00:50:56", // vmware3
    "00:1C:42", // parallels1
    "00:03:FF", // microsoft virtual pc
    "00:0F:4B", // virtual iron 4
    "00:16:3E", // red hat xen , oracle vm , xen source, novell xen
    "08:00:27", // virtualbox
    "00:00:00", // VPN
  ];
  for (let i = 0; i < vmNetwork.length; i++) {
    const mac_per = vmNetwork[i];
    if (mac.startsWith(mac_per)) {
      return true;
    }
  }
  return false;
}

// MD5
export function generateMD5(fileName?: string, languageId?: string, codeBeforeCursor?: string, codeAfterCursor?: string): string {
  const content = `${fileName}${languageId}${codeBeforeCursor}${codeAfterCursor}`;
  const hash = CryptoJS.MD5(content).toString();
  return hash;
}

export function getRelativePath(absolutePath: string) {
  let currentPath = absolutePath;
  let deep = 1;
  const { root } = path.parse(absolutePath);
  while (currentPath !== root) {
    const gitDir = path.join(currentPath, ".git");
    try {
      fs.accessSync(gitDir);
      return path.relative(currentPath, absolutePath);
    }
    catch (err) {
      // .git目录不存在，再检查上一级
    }
    currentPath = path.dirname(currentPath);
    if (deep++ > 500) {
      logger.error(`getRelativePath: 目录层级超过500， 大概率是方法存在bug`);
      break;
    }
  }
  return workspace.asRelativePath(absolutePath); // 如果没有找到.git目录，相对workspace得相对路径
}
