import axios, { CanceledError } from "axios";
import * as vscode from "vscode";
import { getConfig } from "./config";
import curProjectInfos, { getProjectInfo } from "./projectInfo";
import CommonDocument from "./recentDocument";
import { PLUGIN_PLATFORM, PLUGIN_VERSION, buildDeviceLog } from "../log/Model";
import { Stream } from "stream";
import { getRelativePath } from ".";
import { WebloggerManager } from "../base/weblogger";
import { Api } from "../base/http-client";

export let requestTime = -1;
const device = buildDeviceLog();

export type CoilpotResponse = {
  id: string;
  model: string;
  object: string;
  sourceURL: string;
  created: number;
  choices: CoilpotChoice[];
};
type CoilpotChoice = {
  text: string;
  index: number;
  finish_reason: string;
  logprobs: string;
};

// 记录上次请求的Canceltoken，用于检查是否有重复请求
let temp: any;
export async function search(keyword: string, textBeforeCursor: string, textAfterCursor: string, languageId: string, fileName: string, cursorOffset: number, recentlyOpened: CommonDocument[], maxNewTokensForCodeCompletion: number) {
  /*
    从缓存中获取 todo:考虑缓存的时效问题
    if (keyword in cachedResults) {
        return Promise.resolve({results: cachedResults[keyword]});
    }
    */
  const requestRecentlyOpened: CommonDocument[] = [];
  if (recentlyOpened) {
    for (let i = recentlyOpened.length - 1; i >= 0; i--) {
      // 去重复
      if (recentlyOpened[i].fileName != fileName) {
        // 防止文件过大,限制在长度<100000
        const length = recentlyOpened[i].content?.length;
        if (length && length < 100000) {
          requestRecentlyOpened.push(recentlyOpened[i]);
        }
      }
    }
  }
  const config = getConfig();
  const promise = new Promise((resolve, reject) => {
    requestTime = Date.now();
    temp?.("取消请求"); // 取消上一次请求
    axios({
      method: "post",
      url: `${config.settings.proxyUrl}/eapi/kwaipilot/completions`,
      data: {
        text: keyword,
        cursorBeforeText: textBeforeCursor,
        cursorAfterText: textAfterCursor,
        language: languageId,
        count: 3,
        fileName: fileName,
        cursorOffset: cursorOffset,
        modelType: config.settings.modelType,
        viewedDocuments: requestRecentlyOpened,
        projectInfo: curProjectInfos && curProjectInfos.length > 0 ? curProjectInfos[0] : undefined,
        maxNewTokens: maxNewTokensForCodeCompletion,
      },
      cancelToken: new axios.CancelToken((cancel) => {
        // 接受一个 cancel 取消当前请求的方法
        temp = cancel;
      }),
    }).then((response) => {
      if (response?.status == 200 && response?.data && response.data?.status == 200 && response.data.data) {
        vscode.window.setStatusBarMessage(`kwaipilot: Finished loading results`);
        resolve(response.data.data);
      }
      else {
        resolve(null);
      }
    }).catch((err) => {
      if (err instanceof CanceledError) {
        console.log("cancel request...");
        resolve(null);
      }
      else {
        console.log(err);
        reject(err);
      }
    });
  });

  vscode.window.setStatusBarMessage(`kwaipilot: Start loading snippet results...`, promise);
  return promise;
}

const reportCoding = (context: vscode.ExtensionContext, weblogger: WebloggerManager) => {
  const lastReportTime = context.globalState.get("lastReportTime") as string;
  const currentTime = new Date();

  if (lastReportTime) {
    const lastReportDate = new Date(lastReportTime);
    const isSameDay = lastReportDate.toDateString() === currentTime.toDateString();

    if (!isSameDay) {
      // 上报用户使用情况的逻辑
      weblogger.$reportUserAction({ key: "coding" });

      // 更新上次上报时间
      context.globalState.update("lastReportTime", currentTime.toDateString());
    }
  }
  else {
    weblogger.$reportUserAction({ key: "coding" });
    context.globalState.update("lastReportTime", currentTime.toDateString());
  }
};
/** 代码续写请求 - v2 */
export async function requestCodeCompletion(proxyUrl: string, textBeforeCursor: string, textAfterCursor: string, languageId: string,
  fileName: string, cursorOffset: number, deviceId: string | undefined,
  maxNewTokensForCodeCompletion: number, modelType: string, recentlyOpened: CommonDocument[], username: string | undefined, context: vscode.ExtensionContext, weblogger: WebloggerManager, api: Api) {
  const requestRecentlyOpened: CommonDocument[] = [];
  if (recentlyOpened) {
    for (let i = recentlyOpened.length - 1; i >= 0; i--) {
      // 去重复
      if (recentlyOpened[i].fileName != fileName) {
        // 防止文件过大,限制在长度<100000
        const length = recentlyOpened[i].content?.length;
        if (length && length < 100000) {
          requestRecentlyOpened.push(recentlyOpened[i]);
        }
      }
    }
  }
  reportCoding(context, weblogger);
  const startTime = Date.now();
  const projectInfos = getProjectInfo();
  const promise = new Promise((resolve, reject) => {
    const project = projectInfos && projectInfos.length > 0 ? projectInfos[0] : undefined;
    const relativePath = getRelativePath(fileName);
    requestTime = Date.now();
    temp?.("取消请求"); // 取消上一次请求
    axios({
      method: "post",
      url: `${proxyUrl}/eapi/kwaipilot/code/completions/stream`,
      data: {
        projectName: project?.name,
        gitRepo: project?.gitRepo,
        gitRemote: project?.gitRemote,
        currentBranchName: project?.currentBranchName,
        gitUsername: project?.username,
        gitUserEmail: project?.userEmail,
        username: username,

        absolutePath: fileName,
        relativePath,
        language: languageId,

        codeBeforeCursor: textBeforeCursor,
        codeAfterCursor: textAfterCursor,
        cursorOffset: cursorOffset,

        count: 3,
        maxNewTokens: maxNewTokensForCodeCompletion,
        modelType: modelType,
        deviceId: deviceId,
        platform: PLUGIN_PLATFORM,
        pluginVersion: PLUGIN_VERSION,
        deviceName: device.deviceName,
        deviceModel: device.deviceModel,
        deviceOsVersion: device.deviceOsVersion,
        viewedDocuments: requestRecentlyOpened,
        stream: false,
      },
      responseType: "stream",
      cancelToken: new axios.CancelToken((cancel) => {
        // 接受一个 cancel 取消当前请求的方法
        temp = cancel;
      }),
    }).then((response) => {
      const stream = response.data as Stream;
      let data = "";

      stream.on("data", (chunk: string) => {
        data += chunk.toString();
      });

      stream.on("end", () => {
        // 最后一个数据是完整数据
        const ares = data.split("data:").pop() as string;
        try {
          const icon = "$(notebook-state-success)"; // 使用特殊的Unicode字符来表示图标
          vscode.window.setStatusBarMessage(`${icon} Kwaipilot: Finished!`);
          const res = JSON.parse(ares);
          if (res.code === "") {
            if (res.reason === "error") {
              api.reportCost({
                beginTimestamp: startTime,
                namespace: "completions",
                stage: "completion",
                extra1: "count",
                extra2: "error",
              });
            }
            else {
              api.reportCost({
                beginTimestamp: startTime,
                namespace: "completions",
                stage: "completion",
                extra1: "count",
                extra2: "success",
              });
            }
            resolve(null);
          }
          else {
            api.reportCost({
              beginTimestamp: startTime,
              namespace: "completions",
              stage: "completion",
              extra1: "count",
              extra2: "success",
            });
            resolve([res]);
          }
        }
        catch (error) {
          api.reportCost({
            beginTimestamp: startTime,
            namespace: "completions",
            stage: "completion",
            extra1: "count",
            extra2: "error",
          });
          resolve(null);
        }
      });

      stream.on("error", (err) => {
        if (err instanceof CanceledError) {
          api.reportCost({
            beginTimestamp: startTime,
            namespace: "completions",
            stage: "completion",
            extra1: "count",
            extra2: "success",
          });
          resolve(null);
        }
        else {
          api.reportCost({
            beginTimestamp: startTime,
            namespace: "completions",
            stage: "completion",
            extra1: "count",
            extra2: "error",
          });
          reject(err);
        }
      });
    }).catch((err) => {
      if (err instanceof CanceledError) {
        api.reportCost({
          beginTimestamp: startTime,
          namespace: "completions",
          stage: "completion",
          extra1: "count",
          extra2: "success",
        });
        resolve(null);
      }
      else {
        api.reportCost({
          beginTimestamp: startTime,
          namespace: "completions",
          stage: "completion",
          extra1: "count",
          extra2: "error",
        });
        reject(err);
      }
    });
  });
  const icon = "$(loading~spin)"; // 使用特殊的Unicode字符来表示图标
  vscode.window.setStatusBarMessage(`${icon} Kwaipilot: Loading...`, promise);
  return promise;
}
