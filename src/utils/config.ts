import * as vscode from "vscode";

type IConfig = {
  settings: {
    proxyUrl: string;
    modelType: string;
    enable: boolean;
    commentCompletionEnable: boolean;
    maxNewTokensForCodeCompletion: number;
    codeSound: boolean;
    fontSize: number;
    codeCompletionDelay: number;
    enableMidlineAutoCompletions: boolean;
    enableCodeBlockAction: boolean;
    enableOptimizationNotification: boolean;
    notificationInterval: number;
    showInlineTip: boolean;
    enableDiagnosticsCheck: boolean;
  };
};

export function getConfig() {
  const config = vscode.workspace.getConfiguration("kwaipilot");

  const proxyUrl: string = config.settings.proxy;
  const modelType: string = config.settings.modelType;
  const enable: boolean = config.settings.enable;
  const maxNewTokensForCodeCompletion: number = config.settings.maxNewTokensForCodeCompletion;
  const codeSound: boolean = config.settings.codeSound;
  const fontSize: number = config.settings.fontSize;
  const commentCompletionEnable: boolean = config.settings.commentCompletionEnable;
  const codeCompletionDelay: number = config.settings.codeCompletionDelay;
  const enableMidlineAutoCompletions: boolean = config.settings.enableMidlineAutoCompletions;
  const enableCodeBlockAction: boolean = config.settings.enableCodeBlockAction;
  const enableOptimizationNotification: boolean = config.settings.enableOptimizationNotification;
  const notificationInterval: number = config.settings.notificationInterval;
  const showInlineTip: boolean = config.settings.showInlineTip;
  const enableDiagnosticsCheck: boolean = config.settings.enableDiagnosticsCheck;
  return {
    settings: {
      // 去掉结尾的 /
      proxyUrl: (proxyUrl || "https://kinsight.corp.kuaishou.com").replace(".com/", ".com"),
      modelType,
      enable,
      maxNewTokensForCodeCompletion,
      codeSound,
      fontSize,
      commentCompletionEnable,
      codeCompletionDelay,
      enableMidlineAutoCompletions,
      enableCodeBlockAction,
      enableOptimizationNotification,
      notificationInterval,
      showInlineTip,
      enableDiagnosticsCheck,
    },
  } as IConfig;
}
