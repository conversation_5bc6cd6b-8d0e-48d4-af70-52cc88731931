import { SerializedEditorState, SerializedLexicalNode } from "lexical";
import { customVariableItemMentionNodeDisplayText } from "shared/lib/CustomVariable/nodes";
import { isCustomVariableNode } from "shared/lib/CustomVariable";
import { isMentionNode, isSharpCommandMentionNode } from "shared/lib/MentionNode";
import path from "path";

import fs from "fs";
import { extractTextFromFile } from "./extract-text";
import { isBinaryFile } from "isbinaryfile";
import { toPosixPath } from "shared/lib/agent/toPosixPath";
import { isMentionNodeV2, MentionNodeV2Structure } from "shared/lib/MentionNodeV2/nodes";

/**
 * 将用户输入的富文本结构体（editorState）转换为适合 LLM 的，带上下文的文本
 * @param editorState
 * @returns
 */
export async function editorStateToContextualTextForLlm(editorState: SerializedEditorState, cwd: string, contextItems: MentionNodeV2Structure[]): Promise<string> {
  let finalText = "";
  async function walk(node: SerializedLexicalNode) {
    if (isCustomVariableNode(node)) {
      finalText += customVariableItemMentionNodeDisplayText(node.contextItem);
    }
    if (isMentionNode(node)) {
      if (isSharpCommandMentionNode(node)) {
        const mention = node.data;

        // TODO: 路径 normalize？
        const mentionPath = mention;
        // const mentionPath = mention.slice(1);
        try {
          const content = await getFileOrFolderContent(mentionPath, cwd);
          if (mention.endsWith("/")) {
            finalText += `\n\n<folder_content path="${mentionPath}">\n${content}\n</folder_content>`;
          }
          else {
            finalText += `\n\n<file_content path="${mentionPath}">\n${content}\n</file_content>`;
          }
        }
        catch (error) {
          if (mention.endsWith("/")) {
            finalText += `\n\n<folder_content path="${mentionPath}">\nError fetching content: ${(error as Error).message}\n</folder_content>`;
          }
          else {
            finalText += `\n\n<file_content path="${mentionPath}">\nError fetching content: ${(error as Error).message}\n</file_content>`;
          }
        }
      }
      else {
        throw new Error(`Unsupported mention node ${node.commandType}`);
      }
    }
    else if (isMentionNodeV2(node)) {
      const { structure } = node;
      if (structure.type === "file") {
        finalText += `#{file=${structure.relativePath}}`;
      }
      else if (structure.type === "selection") {
        finalText += `#{selection=${structure.relativePath}:L${structure.range.start.line}-L${structure.range.end.line}}`;
      }
      else if (structure.type === "tree") {
        finalText += `#{dir=${structure.relativePath}}`;
      }
      else if (structure.type === "rule") {
        // rule不需要拼接到输入
      }
      else {
        const _never: never = structure;
        throw new Error(`Unsupported mention node ${_never}`);
      }
    }

    else if ("text" in node) {
      finalText += node.text;
    }
    if ("children" in node && Array.isArray(node.children)) {
      for (const child of node.children) {
        await walk(child);
      }
    }
  }
  await walk(editorState.root);

  // 添加 contextItems 的内容
  if (contextItems && contextItems.length > 0) {
    for (const item of contextItems) {
      if (item.type === "selection") {
        finalText += `
<selection path="${item.relativePath}" start_line="${item.range.start.line}" end_line="${item.range.end.line}">
${item.content}
</selection>
`;
      }
      // TODO: @liyun 文件内容
      else if (item.type === "file") {
        finalText += `
<file path="${item.relativePath}">
</file>
`;
      }
      else if (item.type === "tree") {
        finalText += `
<tree path="${item.relativePath}">
</tree>
`;
      }
      else if (item.type === "rule") {
        // rule不需要拼接到输入
      }
      else {
        const _never: never = item;
        throw new Error(`Unsupported context item ${_never}`);
      }
    }
  }

  return finalText;
}

async function getFileOrFolderContent(mentionPath: string, cwd: string): Promise<string> {
  const absPath = path.resolve(cwd, mentionPath);

  try {
    const stats = await fs.statSync(absPath);

    if (stats.isFile()) {
      const isBinary = await isBinaryFile(absPath).catch(() => false);
      if (isBinary) {
        return "(Binary file, unable to display content)";
      }
      const content = await extractTextFromFile(absPath);
      return content;
    }
    else if (stats.isDirectory()) {
      const entries = await fs.readdirSync(absPath, { withFileTypes: true });
      let folderContent = "";
      const fileContentPromises: Promise<string | undefined>[] = [];
      entries.forEach((entry, index) => {
        const isLast = index === entries.length - 1;
        const linePrefix = isLast ? "└── " : "├── ";
        if (entry.isFile()) {
          folderContent += `${linePrefix}${entry.name}\n`;
          const filePath = path.join(mentionPath, entry.name);
          const absoluteFilePath = path.resolve(absPath, entry.name);
          // const relativeFilePath = path.relative(cwd, absoluteFilePath);
          fileContentPromises.push(
            (async () => {
              try {
                const isBinary = await isBinaryFile(absoluteFilePath).catch(() => false);
                if (isBinary) {
                  return undefined;
                }
                const content = await extractTextFromFile(absoluteFilePath);
                return `<file_content path="${toPosixPath(filePath)}">\n${content}\n</file_content>`;
              }
              catch (error) {
                return undefined;
              }
            })(),
          );
        }
        else if (entry.isDirectory()) {
          folderContent += `${linePrefix}${entry.name}/\n`;
          // not recursively getting folder contents
        }
        else {
          folderContent += `${linePrefix}${entry.name}\n`;
        }
      });
      const fileContents = (await Promise.all(fileContentPromises)).filter(content => content);
      return `${folderContent}\n${fileContents.join("\n\n")}`.trim();
    }
    else {
      return `(Failed to read contents of ${mentionPath})`;
    }
  }
  catch (error) {
    throw new Error(`Failed to access path "${mentionPath}": ${(error as Error).message}`);
  }
}
