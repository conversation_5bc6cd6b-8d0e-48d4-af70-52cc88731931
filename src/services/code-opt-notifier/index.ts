import * as vscode from "vscode";
import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { WebloggerManager } from "../../base/weblogger";
import { Webview } from "@webview";
import { v4 as uuidv4 } from "uuid";
import { ConfigManager } from "../../base/state-manager";
import { Config } from "../../base/state-manager/types";
import { LoggerManager } from "../../base/logger";
import { DefaultBaseUrl } from "../../const";
import { Bridge } from "@bridge";
import { WEBVIEW_BRIDGE_EVENT_NAME } from "../../shared/types/bridge";
import { ActionType } from "../../shared/types";

const OptNotifierLog = {
  action: "feature_reco" as const,
  type: {
    opt: "一键优化代码",
    close: "永久关闭",
    pv: "曝光",
  },
};

export class CodeOptNotifier extends ServiceModule {
  private document: vscode.TextDocument | undefined;
  private loading = false;
  private readonly loggerScope = "code-opt-notifiler";
  constructor(ext: ContextManager) {
    super(ext);
    this.initListeners();
  }

  private initListeners() {
    const activeEditor = vscode.window.activeTextEditor;
    if (activeEditor) {
      this.checkAndNotify(activeEditor.document);
    }
    vscode.workspace.onDidOpenTextDocument(this.checkAndNotify.bind(this));
    const cmd = vscode.commands.registerCommand(
      "kwaipilot.codeOptNotifier",
      () => {
        const activeEditor = vscode.window.activeTextEditor;
        if (activeEditor) {
          this.checkAndNotify(activeEditor.document);
        }
      },
    );
    this.context.subscriptions.push(cmd);
  }

  private openView() {
    this.webview.focus("code_action");
    if (this.document) {
      const text = this.document.getText();
      const languageId = this.document.languageId;
      this.bridge.callHandler(this.webview._view!.webview, WEBVIEW_BRIDGE_EVENT_NAME.ACTION_FOR_CODE, {
        type: "optimization" as ActionType,
        selectText: text,
        languageId,
      });
    }
  }

  private checkStatus(document: vscode.TextDocument) {
    // const { settings } = getConfig();
    const enableNotify = this.config.get(
      Config.ENABLE_OPTIMIZATION_NOTIFICATION,
    );
    const maxLine = 200; // 检测文件行数阈值, 暂定200行
    const interval = this.config.get(Config.NOTIFICATION_INTERVAL, 1440);
    const notificationInterval = interval * 60 * 1000; // 消息提示间隔, 24 hours

    const lastNotificationTime
      = this.context.globalState.get<number>("kwaipilot.lastNotificationTime")
      || 0;
    const now = Date.now();
    const allowNotification = now - lastNotificationTime > notificationInterval;
    return enableNotify && document.lineCount > maxLine && allowNotification;
  }

  private async showNotify(text: string, params: any) {
    const Btn = {
      opt: "一键优化代码🚀",
      close: "永久关闭",
    };
    const msg = `亲爱的开发者, 您的代码助手Kwaipilot已经准备就绪, 我们注意到您正在编辑的代码有以下优化建议: ${text} 点击下方按钮, 让Kwaipilot智能优化您的代码, 实现更整洁更高效的编辑体验`;
    this.weblogger.$reportUserAction({
      key: OptNotifierLog.action as any,
      type: OptNotifierLog.type.pv,
      sessionId: params.sessionId,
      chatId: params.chatId,
    });
    const selection = await vscode.window.showInformationMessage(
      msg,
      Btn.opt,
      Btn.close,
    );
    if (selection === Btn.opt) {
      // @todo 一键优化代码
      this.weblogger.$reportUserAction({
        key: OptNotifierLog.action,
        type: OptNotifierLog.type.opt as "一键优化代码",
        sessionId: params.sessionId,
        chatId: params.chatId,
      });
      this.openView();
    }
    else if (selection === Btn.close) {
      this.weblogger.$reportUserAction({
        key: OptNotifierLog.action as any,
        type: OptNotifierLog.type.close as "永久关闭",
        sessionId: params.sessionId,
        chatId: params.chatId,
      });
      this.disableNotifications();
    }
  }

  private disableNotifications() {
    const configuration = vscode.workspace.getConfiguration("kwaipilot");
    configuration.update(
      "settings.enableOptimizationNotification",
      false,
      vscode.ConfigurationTarget.Global,
    );
  }

  private sendOptRequest(params: Record<string, string>) {
    const proxyUrl = this.config.get(Config.PROXY_URL) || DefaultBaseUrl;
    const url = `${proxyUrl}/eapi/kwaipilot/dev-assistant/code_refactor_suggestion`;

    const option = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(params),
    };
    return fetch(url, option).then(res => res.json());
  }

  private async checkAndNotify(document: vscode.TextDocument) {
    try {
      if (this.loading || !this.checkStatus(document)) {
        return;
      }
      this.loading = true;
      this.document = document;
      const code = document.getText();
      const username
        = (this.context.globalState.get("userSsoInfo") as any)?.name || "";
      const params = {
        chatId: uuidv4(),
        code,
        seesionId: uuidv4(),
        username,
      };
      const res = await this.sendOptRequest(params);
      this.loading = false;
      const text = res.data
        .map((t: string, idx: number) => `${idx + 1}.${t}`)
        .join("; ");
      this.showNotify(text, params);
      this.context.globalState.update(
        "kwaipilot.lastNotificationTime",
        Date.now(),
      );
    }
    catch (error) {
      this.logger.error("checkAndNotify Error", this.loggerScope, { err: error });
      console.error("Error:", error);
    }
  }

  private get webview() {
    return this.getBase(Webview);
  }

  private get weblogger() {
    return this.getBase(WebloggerManager);
  }

  private get config() {
    return this.getBase(ConfigManager);
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }

  private get bridge() {
    return this.getBase(Bridge);
  }
}
