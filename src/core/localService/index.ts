import { ChildProcessWithoutNullStreams, spawn } from "child_process";
import { CoreModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { FromCoreProtocol, FromIdeProtocol, ToCoreProtocol, ToIdeProtocol } from "shared/lib/LocalService";
import { IdeCommonMessage, IMessenger, IProtocol, Message } from "../agent/types";
import { LoggerManager } from "../../base/logger";
import { TcpMessenger, CoreBinaryMessenger } from "../agent/messenger";
import { version } from "../../../package.json";

import * as vscode from "vscode";
import { ConfigManager, WorkspaceStateManager } from "../../base/state-manager";
import { SettingPanelModule } from "../setting-panel";
import { Project } from "../project";
import { AgentModule } from "../agent";
import * as path from "path";
import * as os from "os";

export class LocalService extends CoreModule<{ restart: [] }> {
  private messenger!: IMessenger<FromCoreProtocol, ToCoreProtocol>;
  private subprocess!: ChildProcessWithoutNullStreams;
  private isShuttingDown = false;
  private readonly loggerScope = "LocalService";

  private healthCheckInterval!: NodeJS.Timeout;

  private restartAttempts = 0;
  private readonly MAX_RESTART_ATTEMPTS = 10;

  /** 按消息类型存储 messenger 的监听器, 因为内部的 messenger 会初始化多次，所以在这里统一注册一遍 */
  messengerListenersByType = new Map<keyof ToIdeProtocol, ((message: Message) => any)[]>();

  constructor(ext: ContextManager) {
    super(ext);
    this.startSubprocess().catch((reason) => {
      this.logger.error("Failed to start subprocess:", this.loggerScope, {
        err: reason,
      });
    });
    this.startHealthCheck();
  }

  private setupSubprocessListeners() {
    this.subprocess.on("exit", (code, signal) => {
      this.logger.warn("Subprocess exited", this.loggerScope, { value: { code, signal } });
      if (!this.isShuttingDown) {
        this.restartSubprocess();
      }
    });
  }

  dispose() {
    this.isShuttingDown = true;
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    if (this.subprocess) {
      this.subprocess.kill();
    }
    if (this.messenger) {
      this.messenger.dispose();
    }
  }

  private startHealthCheck() {
    this.healthCheckInterval = setInterval(async () => {
      try {
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error("Health check timeout")), 3 * 60 * 1000);
        });

        const pingPromise = this.messenger.request("state/agentState", undefined);
        await Promise.race([pingPromise, timeoutPromise]);
      }
      catch (error) {
        if (this.messenger.checkIsRunning()) {
          return;
        }
        this.logger.error("Health check failed:", this.loggerScope, { err: error });
        await this.restartSubprocess();
      }
    }, 10 * 60 * 1000); // 每十分钟检查一次
  }

  private async restartSubprocess() {
    this.logger.info("Restarting subprocess", this.loggerScope);

    if (this.restartAttempts >= this.MAX_RESTART_ATTEMPTS) {
      this.logger.error("Maximum restart attempts reached. Giving up...", this.loggerScope);
      this.isShuttingDown = true;
      return;
    }

    this.restartAttempts++;
    try {
      // 清理旧的进程和messenger
      if (this.subprocess) {
        this.subprocess.kill("SIGKILL");
      }
      if (this.messenger) {
        this.messenger.dispose();
      }
      // 重新启动
      this.startSubprocess();
      this.emit("restart");
    }
    catch (error) {
      this.logger.error("Error restarting subprocess:", this.loggerScope, { err: error });
    }
  }

  onMessage<T extends keyof ToIdeProtocol>(
    messageType: T,
    handler: (message: Message<ToIdeProtocol[T][0]>) => ToIdeProtocol[T][1],
  ): void {
    if (!this.messengerListenersByType.has(messageType)) {
      this.messengerListenersByType.set(messageType, []);
    }
    this.messengerListenersByType.get(messageType)?.push(handler);

    this.messenger.on(messageType, handler);
  }

  sendMessage<T extends keyof FromIdeProtocol>(messageType: T, data: FromIdeProtocol[T][0], messageId?: string): string {
    return this.messenger.send(messageType, data, messageId);
  }

  request<T extends keyof FromIdeProtocol>(messageType: T, data: FromIdeProtocol[T][0]): Promise<FromIdeProtocol[T][1]> {
    return this.messenger.request(messageType, data);
  }

  private async startSubprocess() {
    const binaryPath = "./kwaipilot-binary";
    const kwaipilotGlobalDir = this.context.extensionPath + "/local-agent";
    try {
      if (process.env.NODE_ENV !== "development") {
        this.subprocess = spawn(binaryPath, {
          env: {
            ...process.env,
            PWD: kwaipilotGlobalDir,
          },
          cwd: kwaipilotGlobalDir,
        });

        this.setupSubprocessListeners();
        this.messenger = new AgentMessenger<FromCoreProtocol, ToCoreProtocol>(this.subprocess, this.project);
      }
      else {
        this.messenger = new TcpMessenger<FromCoreProtocol, ToCoreProtocol>(this.project);
      }
      this.messenger.onError((err, details) => {
        this.logger.error("Error in messenger:", this.loggerScope, {
          err,
          reason: details ?? "",
        });
      });

      this.bindMessengerListeners();

      // TODO: 不应该有这种反向依赖
      setTimeout(() => {
        this.getCore(AgentModule).onLocalServiceConnected();
      }, 0);

      this.logger.info("Successfully spawned subprocess", this.loggerScope);
    }
    catch (error) {
      console.log(error);
      this.logger.error("local agent binary start failed:", this.loggerScope, {
        err: error,
      });
      // throw error;
    }
  }

  private bindMessengerListeners() {
    this.messengerListenersByType.forEach((listeners, messageType) => {
      listeners.forEach(listener => this.messenger.on(messageType, listener));
    });
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }

  private get configManager() {
    return this.getBase(ConfigManager);
  }

  private get settingPanel() {
    return this.getCore(SettingPanelModule);
  }

  private get workspaceState() {
    return this.getBase(WorkspaceStateManager);
  }

  private get project() {
    return this.getCore(Project);
  }
}

class AgentMessenger<T extends IProtocol, U extends IProtocol> extends CoreBinaryMessenger<T, U> {
  constructor(subprocess: ChildProcessWithoutNullStreams, private project: Project) {
    super(subprocess);
  }

  getCommonMessage(): IdeCommonMessage {
    const repoPath = this.project.getRepoPath() || "";
    const gitUrl = this.project.getRemoteOriginUrl(repoPath) || "";
    return {
      version: vscode.version,
      platform: "vscode",
      pluginVersion: version,
      cwd: vscode.workspace.workspaceFolders?.map(folder => folder.uri.fsPath).at(0) ?? path.join(os.homedir(), "Desktop"),
      repo: {
        git_url: gitUrl,
        dir_path: repoPath,
        commit: this.project.getCurrentCommit() || "",
      },
    };
  }
}
