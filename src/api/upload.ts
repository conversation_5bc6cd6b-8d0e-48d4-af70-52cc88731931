import axios, { AxiosProgressEvent, CanceledError } from "axios";
import * as vscode from "vscode";
import * as fs from "fs";
import { getConfig } from "../utils/config";
import { fromFile } from "file-type";
import { v4 as uuidv4 } from "uuid";

export interface UploadParams {
  file: vscode.Uri;
  onStart?: (file: UploadFile) => void;
  onProgress?: (file: UploadFile) => void;
  onSuccess?: (data: UploadFile) => void;
  onFailed?: (file: UploadFile) => void;
}
export interface UploadFile {
  biz?: string;
  filename: string;
  id?: number;
  uid: string;
  path?: string;
  url?: string;
  type?: string;
  size: number;
  username?: string;
  progress?: number;
  status?: "uploading" | "done" | "error";
  [key: string]: any;
}

export const upload = async ({ file, onStart, onProgress, onSuccess, onFailed }: UploadParams) => {
  const config = getConfig();
  const fileStream = fs.readFileSync(file.path);
  const filename = file.path.split("/").pop();
  const fileType = await fromFile(file.path);
  let blob;
  let formData: any;
  try {
    blob = new Blob([fileStream], { type: fileType?.mime });
    formData = new FormData();
    formData.append("file", blob, filename);
  }
  catch (e) {
    const { FormData, Blob } = await import("formdata-node");
    const { ReadableStream } = await import("web-streams-polyfill");
    if (!global.ReadableStream) {
      global.ReadableStream = ReadableStream as any;
    }
    blob = new Blob([fileStream], { type: fileType?.mime });
    formData = new FormData();
    formData.append("file", blob, filename);
    console.log(ReadableStream);
  }

  const fileInfo: UploadFile = {
    filename: filename || "",
    type: fileType?.mime,
    size: fileStream.length,
    uid: uuidv4(),
    status: "uploading",
    progress: 0,
  };
  onStart?.(fileInfo);
  const promise = new Promise((resolve, reject) => {
    // temp?.("取消请求"); // 取消上一次请求
    axios({
      method: "post",
      url: `${config.settings.proxyUrl}/eapi/kwaipilot/file/upload`,
      data: formData,
      onUploadProgress: (e: AxiosProgressEvent) => {
        onProgress?.({
          ...fileInfo,
          progress: e.loaded / (e.total || e.bytes) * 100,
          status: "uploading",
        });
      },
    }).then((response) => {
      if (response?.status == 200 && response?.data && response.data?.status == 200 && response.data.data) {
        // console.log('success',response.data.data);
        onSuccess?.({
          ...fileInfo,
          ...response.data.data,
          progress: 100,
          status: "done",
        });
        resolve(response.data.data);
      }
      else {
        onFailed?.({ ...fileInfo, status: "error" });
        resolve(null);
      }
    }).catch((err) => {
      if (err instanceof CanceledError) {
        console.log("cancel request...");
        resolve(null);
      }
      else {
        console.log(err);
        onFailed?.({ ...fileInfo, status: "error" });
        reject(err);
      }
    });
  });
  return promise;
};
